from flask_login import UserMixin
from config import login_manager, bcrypt, MYSQL_CONFIG
import mysql.connector

@login_manager.user_loader
def load_user(user_id):
    return User.get(user_id)

class User(UserMixin):
    def __init__(self, user_data):
        self.id = user_data['id']
        self.username = user_data['username']
        self.password = user_data['password']  # Plain text password
        self.role_id = user_data['role_id']
        self.role_name = user_data.get('role_name')
        self._is_active = user_data.get('is_active', True)

    @property
    def is_active(self):
        return self._is_active

    @is_active.setter
    def is_active(self, value):
        self._is_active = value

    @staticmethod
    def get(user_id):
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor(dictionary=True)
        cursor.execute('''
            SELECT u.*, r.name as role_name 
            FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = %s
        ''', (user_id,))
        user_data = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if user_data:
            return User(user_data)
        return None

    @staticmethod
    def get_by_username(username):
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute('''
                SELECT u.*, r.name as role_name 
                FROM users u
                JOIN roles r ON u.role_id = r.id
                WHERE u.username = %s
            ''', (username,))
            user_data = cursor.fetchone()
            if user_data:
                return User(user_data)
            return None
        finally:
            cursor.close()
            conn.close()
        
        if user_data:
            return User(
                id=user_data['id'],
                username=user_data['username'],
                password_hash=user_data['password'],
                role_id=user_data['role_id']
            )
        return None

    def verify_password(self, password):
        return bcrypt.check_password_hash(self.password_hash, password)

@login_manager.user_loader
def load_user(user_id):
    return User.get(user_id)
