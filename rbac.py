from functools import wraps
from flask import jsonify, request, session, redirect, url_for
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity
import mysql.connector
from app import MYSQL_CONFIG

def get_user_role(user_id):
    """Get user's role from database"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute('''
            SELECT r.name as role_name 
            FROM system_users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = %s
        ''', (user_id,))
        result = cursor.fetchone()
        return result['role_name'] if result else None
    finally:
        cursor.close()
        conn.close()

def role_required(*allowed_roles):
    """Decorator to check if user has required role"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                verify_jwt_in_request()
                current_user_id = get_jwt_identity()
                user_role = get_user_role(current_user_id)
                
                if not user_role or user_role not in allowed_roles:
                    return jsonify({
                        "msg": f"This action requires one of these roles: {', '.join(allowed_roles)}"
                    }), 403
                
                return f(*args, **kwargs)
            except Exception as e:
                return jsonify({"msg": "Authentication failed"}), 401
        return decorated_function
    return decorator

def require_login(f):
    """Decorator to ensure user is logged in for web pages"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            verify_jwt_in_request()
            return f(*args, **kwargs)
        except:
            return redirect(url_for('login'))
    return decorated_function

def init_admin_user():
    """Create initial admin user if none exists"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        # Check if any admin exists
        cursor.execute('''
            SELECT COUNT(*) as count 
            FROM system_users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE r.name = 'admin'
        ''')
        result = cursor.fetchone()
        
        if result['count'] == 0:
            # Get admin role id
            cursor.execute("SELECT id FROM roles WHERE name = 'admin'")
            admin_role = cursor.fetchone()
            
            if admin_role:
                from auth import bcrypt
                # Create default admin user
                password_hash = bcrypt.generate_password_hash('admin123').decode('utf-8')
                cursor.execute('''
                    INSERT INTO system_users (
                        username, password_hash, email, role_id, is_active
                    ) VALUES (%s, %s, %s, %s, %s)
                ''', ('admin', password_hash, '<EMAIL>', admin_role['id'], True))
                conn.commit()
                print("Default admin user created. Username: admin, Password: admin123")
    finally:
        cursor.close()
        conn.close()
