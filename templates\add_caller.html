{% extends "base.html" %}

{% block title %}Add Caller - Servicedesk Automated Assistant{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-user-plus"></i> Add New Caller</h2>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                                    {{ message }}
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST" action="{{ url_for('add_caller_form') }}">
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="balance" class="form-label">Balance</label>
                            <input type="number" step="0.01" class="form-control" id="balance" name="balance" required>
                        </div>

                        <div class="mb-3">
                            <label for="debt" class="form-label">Debt</label>
                            <input type="number" step="0.01" class="form-control" id="debt" name="debt" required>
                        </div>

                        <div class="mb-3">
                            <label for="discount" class="form-label">Discount</label>
                            <input type="number" step="0.01" class="form-control" id="discount" name="discount" required>
                        </div>

                        <div class="mb-3">
                            <label for="birthday" class="form-label">Birthday</label>
                            <input type="date" class="form-control" id="birthday" name="birthday" required>
                        </div>

                        <div class="mb-3">
                            <label for="card_number" class="form-label">Card Number</label>
                            <input type="text" class="form-control" id="card_number" name="card_number" required>
                        </div>

                        <div class="mb-3">
                            <label for="due_date" class="form-label">Due Date</label>
                            <input type="date" class="form-control" id="due_date" name="due_date" required>
                        </div>

                        <div class="mb-3">
                            <label for="discount_due_date" class="form-label">Discount Due Date</label>
                            <input type="date" class="form-control" id="discount_due_date" name="discount_due_date" required>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Add Caller
                            </button>
                            <a href="{{ url_for('index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
