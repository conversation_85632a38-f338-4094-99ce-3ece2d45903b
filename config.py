from flask import Flask
from flask_jwt_extended import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask_bcrypt import Bcrypt
from flask_cors import CORS
from flask_login import Login<PERSON>anager
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Flask app configuration
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-here')
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'your-jwt-secret-key-here')

# Initialize extensions
jwt = JWTManager(app)
bcrypt = Bcrypt(app)
CORS(app)
login_manager = LoginManager(app)
login_manager.login_view = 'login'

# MySQL Configuration
MYSQL_CONFIG = {
    'host': os.getenv('MYSQL_HOST', 'localhost'),
    'user': os.getenv('MYSQL_USER', 'root'),
    'password': os.getenv('MYSQL_PASSWORD', ''),
    'database': os.getenv('MYSQL_DATABASE', 'servicedesk'),
    'port': int(os.getenv('MYSQL_PORT', '3306'))
}
