{% extends "base.html" %}

{% block title %}Add Call Log{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-phone-plus"></i> Add Call Log</h5>
                <small class="text-muted">For testing purposes - normally this would be automated by the agent system</small>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="caller_name" class="form-label">Caller Name</label>
                                <input type="text" class="form-control" id="caller_name" name="caller_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="card_number" class="form-label">Card Number</label>
                                <input type="text" class="form-control" id="card_number" name="card_number" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user_id" class="form-label">Associated User (Optional)</label>
                                <select class="form-control" id="user_id" name="user_id">
                                    <option value="">Select User (if verified)</option>
                                    {% for user in users %}
                                    <option value="{{ user.id }}">{{ user.name }} ({{ user.card_number }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="call_status" class="form-label">Call Status</label>
                                <select class="form-control" id="call_status" name="call_status" required>
                                    <option value="completed">Completed</option>
                                    <option value="disconnected">Disconnected</option>
                                    <option value="transferred">Transferred to Agent</option>
                                    <option value="failed">Failed</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="call_start_time" class="form-label">Call Start Time</label>
                                <input type="datetime-local" class="form-control" id="call_start_time" name="call_start_time" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="call_end_time" class="form-label">Call End Time</label>
                                <input type="datetime-local" class="form-control" id="call_end_time" name="call_end_time" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="verification_attempts" class="form-label">Verification Attempts</label>
                                <input type="number" class="form-control" id="verification_attempts" name="verification_attempts" min="1" max="10" value="1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="follow_up_choice" class="form-label">Follow-up Choice</label>
                                <select class="form-control" id="follow_up_choice" name="follow_up_choice" required>
                                    <option value="payment_details">Send Payment Details</option>
                                    <option value="repeat_info">Repeat Information</option>
                                    <option value="agent_transfer">Transfer to Agent</option>
                                    <option value="hang_up">Hang Up</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Interaction Options</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="verification_successful" name="verification_successful">
                                    <label class="form-check-label" for="verification_successful">
                                        Verification Successful
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="information_repeated" name="information_repeated">
                                    <label class="form-check-label" for="information_repeated">
                                        Information Repeated
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="requested_agent" name="requested_agent">
                                    <label class="form-check-label" for="requested_agent">
                                        Requested Agent
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="agreed_to_payment_details" name="agreed_to_payment_details">
                                    <label class="form-check-label" for="agreed_to_payment_details">
                                        Agreed to Payment Details
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="payment_method_interest" class="form-label">Payment Method Interest</label>
                                <input type="text" class="form-control" id="payment_method_interest" name="payment_method_interest" 
                                       placeholder="e.g., Bank Transfer, GCash, Maya">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="Additional notes about the call..."></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('call_logs') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Call Logs
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Add Call Log
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Set default times
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const startTime = new Date(now.getTime() - 5 * 60000); // 5 minutes ago
    
    document.getElementById('call_start_time').value = startTime.toISOString().slice(0, 16);
    document.getElementById('call_end_time').value = now.toISOString().slice(0, 16);
});
</script>
{% endblock %}
