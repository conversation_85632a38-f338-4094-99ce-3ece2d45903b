{% extends "base.html" %}

{% block title %}Prompt Management - Servicedesk Automated Assistant{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2><i class="fas fa-comment-dots"></i> Prompt Management</h2>
    </div>
    {% if current_user.role_name in ['admin'] %}
    <div class="col-auto">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPromptModal">
            <i class="fas fa-plus"></i> Create New Prompt
        </button>
    </div>
    {% endif %}
</div>

<!-- Prompt List -->
<div class="card shadow mb-4">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Prompts</h5>
            </div>
            <div class="col-auto">
                <div class="input-group">
                    <select class="form-select" id="sectionFilter">
                        <option value="">All Sections</option>
                        <option value="welcome">Welcome</option>
                        <option value="body">Body</option>
                        <option value="offers">Offers</option>
                        <option value="options">Options</option>
                    </select>
                    <select class="form-select" id="languageFilter">
                        <option value="">All Languages</option>
                        <!-- Languages will be populated from API -->
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Section</th>
                        <th>Name</th>
                        <th>Language</th>
                        <th>Status</th>
                        <th>Toggle</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="promptsTableBody">
                    <!-- Prompts will be populated here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create/Edit Prompt Modal -->
<div class="modal fade" id="promptModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promptModalTitle">Create New Prompt</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="promptForm">
                    <input type="hidden" id="promptId">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Section</label>
                            <select class="form-select" id="promptSection" required>
                                <option value="welcome">Welcome</option>
                                <option value="body">Body</option>
                                <option value="offers">Offers</option>
                                <option value="options">Options</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Language</label>
                            <select class="form-select" id="promptLanguage" required>
                                <!-- Languages will be populated from API -->
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Name</label>
                        <input type="text" class="form-control" id="promptName" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Content</label>
                        <div class="input-group mb-2">
                            <textarea class="form-control" id="promptContent" rows="5" required></textarea>
                            <button type="button" class="btn btn-outline-secondary" id="insertVariable">
                                <i class="fas fa-plus"></i> Insert Variable
                            </button>
                        </div>
                        <div class="form-text">
                            Use variables like {Name}, {Balance}, etc. Click "Insert Variable" to add from available options.
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="promptActive" checked>
                                <label class="form-check-label">Active</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="promptToggle">
                                <label class="form-check-label">Enable Toggle</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="previewPrompt">
                    <i class="fas fa-play"></i> Preview
                </button>
                <button type="button" class="btn btn-primary" id="savePrompt">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Variable Selection Modal -->
<div class="modal fade" id="variableModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Insert Variable</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <select class="form-select" id="variableSelect">
                    <!-- Variables will be populated from API -->
                </select>
                <div class="form-text mt-2" id="variableDescription">
                    Select a variable to see its description.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="insertSelectedVariable">Insert</button>
            </div>
        </div>
    </div>
</div>

<script>
// Load prompts from API
async function loadPrompts() {
    try {
        const response = await fetch('/api/prompts', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        const data = await response.json();
        
        const tbody = document.getElementById('promptsTableBody');
        tbody.innerHTML = '';
        
        data.prompts.forEach(prompt => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${prompt.section}</td>
                <td>${prompt.name}</td>
                <td>${prompt.language_name}</td>
                <td>
                    <span class="badge bg-${prompt.is_active ? 'success' : 'danger'}">
                        ${prompt.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${prompt.is_toggle_enabled ? 'info' : 'secondary'}">
                        ${prompt.is_toggle_enabled ? 'Enabled' : 'Disabled'}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editPrompt(${prompt.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="previewPrompt(${prompt.id})">
                        <i class="fas fa-play"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('Error loading prompts:', error);
        alert('Failed to load prompts');
    }
}

// Load languages from API
async function loadLanguages() {
    try {
        const response = await fetch('/api/languages', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        const data = await response.json();
        
        const languageFilter = document.getElementById('languageFilter');
        const promptLanguage = document.getElementById('promptLanguage');
        
        data.languages.forEach(lang => {
            const option = new Option(lang.name, lang.code);
            languageFilter.add(option.cloneNode(true));
            promptLanguage.add(option);
        });
    } catch (error) {
        console.error('Error loading languages:', error);
    }
}

// Load variables from API
async function loadVariables() {
    try {
        const response = await fetch('/api/variables', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        const data = await response.json();
        
        const variableSelect = document.getElementById('variableSelect');
        variableSelect.innerHTML = '';
        
        data.variables.forEach(variable => {
            const option = new Option(variable.name, variable.name);
            option.dataset.description = variable.description;
            variableSelect.add(option);
        });
    } catch (error) {
        console.error('Error loading variables:', error);
    }
}

// Preview prompt using TTS
async function previewPrompt(promptId) {
    try {
        const content = promptId ? 
            (await fetch(`/api/prompts/${promptId}`, {
                headers: {'Authorization': `Bearer ${localStorage.getItem('token')}`}
            }).then(r => r.json())).content :
            document.getElementById('promptContent').value;
            
        const response = await fetch('/api/tts/preview', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ content })
        });
        
        if (response.ok) {
            const audio = new Audio(URL.createObjectURL(await response.blob()));
            audio.play();
        } else {
            throw new Error('Preview failed');
        }
    } catch (error) {
        console.error('Error previewing prompt:', error);
        alert('Failed to preview prompt');
    }
}

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    loadPrompts();
    loadLanguages();
    loadVariables();
    
    // Initialize Select2 for better dropdowns
    $('#variableSelect').select2({
        dropdownParent: $('#variableModal'),
        placeholder: 'Select a variable'
    });
    
    // Variable description update
    $('#variableSelect').on('change', function() {
        const option = this.options[this.selectedIndex];
        document.getElementById('variableDescription').textContent = 
            option ? option.dataset.description : 'Select a variable to see its description.';
    });
    
    // Filter handlers
    $('#sectionFilter, #languageFilter').on('change', loadPrompts);
    
    // Save prompt handler
    document.getElementById('savePrompt').addEventListener('click', async () => {
        // Implementation of save functionality
    });
    
    // Variable insertion handler
    document.getElementById('insertSelectedVariable').addEventListener('click', () => {
        const variable = document.getElementById('variableSelect').value;
        const content = document.getElementById('promptContent');
        const cursorPos = content.selectionStart;
        
        content.value = 
            content.value.substring(0, cursorPos) +
            `{${variable}}` +
            content.value.substring(content.selectionEnd);
            
        $('#variableModal').modal('hide');
    });
});
</script>
{% endblock %}
