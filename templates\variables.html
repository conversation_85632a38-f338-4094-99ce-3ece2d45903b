{% extends "base.html" %}

{% block title %}Variable Management - Servicedesk Automated Assistant{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2><i class="fas fa-code"></i> Variable Management</h2>
    </div>
    {% if current_user.role_name in ['admin'] %}
    <div class="col-auto">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#variableModal">
            <i class="fas fa-plus"></i> Create New Variable
        </button>
    </div>
    {% endif %}
</div>

<!-- Variable List -->
<div class="card shadow">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Variables</h5>
            </div>
            <div class="col-auto">
                <input type="text" class="form-control" id="variableSearch" placeholder="Search variables...">
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Data Type</th>
                        <th>Default Value</th>
                        <th>Created By</th>
                        <th>Updated</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="variablesTableBody">
                    <!-- Variables will be populated here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create/Edit Variable Modal -->
<div class="modal fade" id="variableModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="variableModalTitle">Create New Variable</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="variableForm">
                    <input type="hidden" id="variableId">
                    
                    <div class="mb-3">
                        <label class="form-label">Name</label>
                        <input type="text" class="form-control" id="variableName" required
                               pattern="[A-Za-z][A-Za-z0-9_]*"
                               title="Start with a letter, use only letters, numbers, and underscore">
                        <div class="form-text">
                            This is how you'll reference the variable in prompts using {VariableName}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" id="variableDescription" rows="2" required></textarea>
                        <div class="form-text">
                            Explain what this variable represents and how it should be used
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Data Type</label>
                        <select class="form-select" id="variableType" required>
                            <option value="text">Text</option>
                            <option value="number">Number</option>
                            <option value="date">Date</option>
                            <option value="boolean">Boolean</option>
                            <option value="currency">Currency</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Default Value (Optional)</label>
                        <input type="text" class="form-control" id="variableDefault">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveVariable">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
// Load variables from API
async function loadVariables(searchTerm = '') {
    try {
        const response = await fetch('/api/variables', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        const data = await response.json();
        
        const tbody = document.getElementById('variablesTableBody');
        tbody.innerHTML = '';
        
        data.variables
            .filter(v => !searchTerm || 
                v.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                v.description.toLowerCase().includes(searchTerm.toLowerCase()))
            .forEach(variable => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><code>{${variable.name}}</code></td>
                    <td>${variable.description}</td>
                    <td>${variable.data_type}</td>
                    <td>${variable.default_value || ''}</td>
                    <td>${variable.created_by_user}</td>
                    <td>${new Date(variable.updated_at).toLocaleDateString()}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editVariable(${variable.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
    } catch (error) {
        console.error('Error loading variables:', error);
        alert('Failed to load variables');
    }
}

// Edit variable
async function editVariable(id) {
    try {
        const response = await fetch(`/api/variables/${id}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        const variable = await response.json();
        
        document.getElementById('variableId').value = variable.id;
        document.getElementById('variableName').value = variable.name;
        document.getElementById('variableDescription').value = variable.description;
        document.getElementById('variableType').value = variable.data_type;
        document.getElementById('variableDefault').value = variable.default_value || '';
        
        document.getElementById('variableModalTitle').textContent = 'Edit Variable';
        $('#variableModal').modal('show');
    } catch (error) {
        console.error('Error loading variable:', error);
        alert('Failed to load variable details');
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    loadVariables();
    
    // Search handler
    let searchTimeout;
    document.getElementById('variableSearch').addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => loadVariables(e.target.value), 300);
    });
    
    // Save variable handler
    document.getElementById('saveVariable').addEventListener('click', async () => {
        const id = document.getElementById('variableId').value;
        const data = {
            name: document.getElementById('variableName').value,
            description: document.getElementById('variableDescription').value,
            data_type: document.getElementById('variableType').value,
            default_value: document.getElementById('variableDefault').value
        };
        
        try {
            const url = id ? `/api/variables/${id}` : '/api/variables';
            const method = id ? 'PUT' : 'POST';
            
            const response = await fetch(url, {
                method,
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            if (response.ok) {
                $('#variableModal').modal('hide');
                loadVariables();
            } else {
                throw new Error('Failed to save variable');
            }
        } catch (error) {
            console.error('Error saving variable:', error);
            alert('Failed to save variable');
        }
    });
    
    // Reset form when modal is opened for new variable
    $('#variableModal').on('show.bs.modal', function(e) {
        if (!e.relatedTarget) return; // Don't reset if opened by edit button
        
        document.getElementById('variableForm').reset();
        document.getElementById('variableId').value = '';
        document.getElementById('variableModalTitle').textContent = 'Create New Variable';
    });
});
</script>
{% endblock %}
