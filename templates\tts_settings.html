{% extends "base.html" %}

{% block title %}TTS Configuration - Servicedesk Automated Assistant{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2><i class="fas fa-microphone"></i> TTS Configuration</h2>
    </div>
</div>

<!-- TTS Engines -->
<div class="card shadow mb-4">
    <div class="card-header">
        <h5 class="mb-0">TTS Engines</h5>
    </div>
    <div class="card-body">
        <form id="ttsForm">
            <!-- Engine Selection -->
            <div class="mb-4">
                <label class="form-label">Active TTS Engine</label>
                <select class="form-select" id="activeEngine" name="activeEngine">
                    <option value="google">Google Cloud TTS</option>
                    <option value="aws">Amazon Polly</option>
                    <option value="local">Local TTS</option>
                </select>
            </div>

            <!-- Google Cloud TTS Settings -->
            <div class="engine-settings" id="googleSettings">
                <h5 class="border-bottom pb-2">Google Cloud TTS Settings</h5>
                <div class="mb-3">
                    <label class="form-label">API Key</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="googleApiKey" name="googleApiKey">
                        <button class="btn btn-outline-secondary toggle-password" type="button">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Default Voice</label>
                    <select class="form-select" id="googleVoice" name="googleVoice">
                        <!-- Will be populated via API -->
                    </select>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Speaking Rate</label>
                            <input type="range" class="form-range" id="googleRate" name="googleRate"
                                   min="0.25" max="4.0" step="0.25" value="1.0">
                            <div class="form-text text-center" id="googleRateValue">1.0</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Pitch</label>
                            <input type="range" class="form-range" id="googlePitch" name="googlePitch"
                                   min="-20.0" max="20.0" step="1.0" value="0.0">
                            <div class="form-text text-center" id="googlePitchValue">0.0</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Amazon Polly Settings -->
            <div class="engine-settings d-none" id="awsSettings">
                <h5 class="border-bottom pb-2">Amazon Polly Settings</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Access Key ID</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="awsAccessKey" name="awsAccessKey">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Secret Access Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="awsSecretKey" name="awsSecretKey">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Region</label>
                            <select class="form-select" id="awsRegion" name="awsRegion">
                                <option value="us-east-1">US East (N. Virginia)</option>
                                <option value="us-west-2">US West (Oregon)</option>
                                <option value="eu-west-1">EU (Ireland)</option>
                                <option value="ap-northeast-1">Asia Pacific (Tokyo)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Voice ID</label>
                            <select class="form-select" id="awsVoice" name="awsVoice">
                                <!-- Will be populated via API -->
                            </select>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Engine</label>
                    <select class="form-select" id="awsEngine" name="awsEngine">
                        <option value="standard">Standard</option>
                        <option value="neural">Neural</option>
                    </select>
                </div>
            </div>

            <!-- Local TTS Settings -->
            <div class="engine-settings d-none" id="localSettings">
                <h5 class="border-bottom pb-2">Local TTS Settings</h5>
                <div class="mb-3">
                    <label class="form-label">Voice</label>
                    <select class="form-select" id="localVoice" name="localVoice">
                        <!-- Will be populated with system voices -->
                    </select>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Rate</label>
                            <input type="range" class="form-range" id="localRate" name="localRate"
                                   min="0.1" max="2.0" step="0.1" value="1.0">
                            <div class="form-text text-center" id="localRateValue">1.0</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Volume</label>
                            <input type="range" class="form-range" id="localVolume" name="localVolume"
                                   min="0.0" max="1.0" step="0.1" value="1.0">
                            <div class="form-text text-center" id="localVolumeValue">1.0</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Area -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Test TTS</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Sample Text</label>
                        <textarea class="form-control" id="testText" rows="3"
                                 placeholder="Enter text to test TTS settings..."></textarea>
                    </div>
                    <button type="button" class="btn btn-primary" id="testTTS">
                        <i class="fas fa-play"></i> Test
                    </button>
                </div>
            </div>

            <!-- Save Button -->
            <div class="text-end mt-4">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    // Load current settings
    loadTTSSettings();
    
    // Engine selection handler
    document.getElementById('activeEngine').addEventListener('change', function() {
        document.querySelectorAll('.engine-settings').forEach(el => el.classList.add('d-none'));
        document.getElementById(`${this.value}Settings`).classList.remove('d-none');
    });
    
    // Range input value displays
    const ranges = {
        'googleRate': 'googleRateValue',
        'googlePitch': 'googlePitchValue',
        'localRate': 'localRateValue',
        'localVolume': 'localVolumeValue'
    };
    
    Object.entries(ranges).forEach(([input, display]) => {
        const inputEl = document.getElementById(input);
        const displayEl = document.getElementById(display);
        
        inputEl.addEventListener('input', () => {
            displayEl.textContent = inputEl.value;
        });
    });
    
    // Password toggle buttons
    document.querySelectorAll('.toggle-password').forEach(button => {
        button.addEventListener('click', function() {
            const input = this.previousElementSibling;
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
    
    // Test TTS
    document.getElementById('testTTS').addEventListener('click', async () => {
        const text = document.getElementById('testText').value;
        if (!text) return;
        
        try {
            const response = await fetch('/api/tts/test', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text,
                    engine: document.getElementById('activeEngine').value,
                    settings: getCurrentEngineSettings()
                })
            });
            
            if (response.ok) {
                const audio = new Audio(URL.createObjectURL(await response.blob()));
                audio.play();
            } else {
                throw new Error('TTS test failed');
            }
        } catch (error) {
            console.error('Error testing TTS:', error);
            alert('Failed to test TTS settings');
        }
    });
    
    // Save settings
    document.getElementById('ttsForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        try {
            const response = await fetch('/api/tts-settings', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    engine: document.getElementById('activeEngine').value,
                    settings: getCurrentEngineSettings()
                })
            });
            
            if (response.ok) {
                alert('Settings saved successfully');
            } else {
                throw new Error('Failed to save settings');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            alert('Failed to save settings');
        }
    });
});

// Load current TTS settings
async function loadTTSSettings() {
    try {
        const response = await fetch('/api/tts-settings', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (response.ok) {
            const settings = await response.json();
            applySettings(settings);
        }
    } catch (error) {
        console.error('Error loading TTS settings:', error);
    }
}

// Get current engine settings
function getCurrentEngineSettings() {
    const engine = document.getElementById('activeEngine').value;
    
    switch (engine) {
        case 'google':
            return {
                apiKey: document.getElementById('googleApiKey').value,
                voice: document.getElementById('googleVoice').value,
                rate: document.getElementById('googleRate').value,
                pitch: document.getElementById('googlePitch').value
            };
            
        case 'aws':
            return {
                accessKey: document.getElementById('awsAccessKey').value,
                secretKey: document.getElementById('awsSecretKey').value,
                region: document.getElementById('awsRegion').value,
                voice: document.getElementById('awsVoice').value,
                engine: document.getElementById('awsEngine').value
            };
            
        case 'local':
            return {
                voice: document.getElementById('localVoice').value,
                rate: document.getElementById('localRate').value,
                volume: document.getElementById('localVolume').value
            };
    }
}

// Apply loaded settings to form
function applySettings(settings) {
    const { engine, settings: engineSettings } = settings;
    
    document.getElementById('activeEngine').value = engine;
    document.querySelectorAll('.engine-settings').forEach(el => el.classList.add('d-none'));
    document.getElementById(`${engine}Settings`).classList.remove('d-none');
    
    switch (engine) {
        case 'google':
            document.getElementById('googleApiKey').value = engineSettings.apiKey || '';
            document.getElementById('googleVoice').value = engineSettings.voice || '';
            document.getElementById('googleRate').value = engineSettings.rate || 1.0;
            document.getElementById('googlePitch').value = engineSettings.pitch || 0.0;
            break;
            
        case 'aws':
            document.getElementById('awsAccessKey').value = engineSettings.accessKey || '';
            document.getElementById('awsSecretKey').value = engineSettings.secretKey || '';
            document.getElementById('awsRegion').value = engineSettings.region || 'us-east-1';
            document.getElementById('awsVoice').value = engineSettings.voice || '';
            document.getElementById('awsEngine').value = engineSettings.engine || 'standard';
            break;
            
        case 'local':
            document.getElementById('localVoice').value = engineSettings.voice || '';
            document.getElementById('localRate').value = engineSettings.rate || 1.0;
            document.getElementById('localVolume').value = engineSettings.volume || 1.0;
            break;
    }
}
</script>
{% endblock %}
