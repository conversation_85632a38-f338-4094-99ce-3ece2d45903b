# Web Routes
@app.route('/')
@require_login
def index():
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = get_user_by_username(username)
        if not user or not bcrypt.check_password_hash(user['password_hash'], password):
            flash('Invalid username or password', 'error')
            return redirect(url_for('login'))
        
        if not user['is_active']:
            flash('Your account is disabled', 'error')
            return redirect(url_for('login'))
        
        # Create JWT token
        access_token = create_access_token(identity=user['id'])
        session['token'] = access_token
        
        # Update last login
        update_last_login(user['id'])
        
        # Log the successful login
        log_audit(
            user['id'],
            'login',
            f"User {username} logged in via web interface",
            request.remote_addr
        )
        
        return redirect(url_for('index'))
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    if 'token' in session:
        # Log the logout
        try:
            verify_jwt_in_request()
            current_user_id = get_jwt_identity()
            log_audit(
                current_user_id,
                'logout',
                "User logged out",
                request.remote_addr
            )
        except:
            pass
        
        session.pop('token', None)
    return redirect(url_for('login'))

@app.route('/profile')
@require_login
def profile():
    current_user_id = get_jwt_identity()
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute('''
            SELECT u.*, r.name as role_name 
            FROM system_users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = %s
        ''', (current_user_id,))
        user = cursor.fetchone()
        return render_template('profile.html', user=user)
    finally:
        cursor.close()
        conn.close()

@app.route('/user-management')
@role_required('admin')
def user_management():
    return render_template('user_management.html')

@app.route('/reports')
@role_required('admin', 'supervisor', 'standard')
def reports():
    return render_template('reports.html')

@app.route('/audit-logs')
@role_required('admin', 'supervisor')
def audit_logs():
    return render_template('audit_logs.html')

# Error handlers
@app.errorhandler(401)
def unauthorized_error(error):
    if request.is_json:
        return jsonify({"msg": "Unauthorized"}), 401
    return redirect(url_for('login'))

@app.errorhandler(403)
def forbidden_error(error):
    if request.is_json:
        return jsonify({"msg": "Forbidden"}), 403
    flash('You do not have permission to access this page', 'error')
    return redirect(url_for('index'))

# Initialize admin user on startup
init_admin_user()
