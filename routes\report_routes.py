from flask import jsonify, request, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import app, MYSQL_CONFIG
from auth import log_audit
from rbac import role_required
import mysql.connector
import csv
from io import StringIO
from datetime import datetime, timedelta

@app.route('/api/reports/call-logs', methods=['GET'])
@role_required('admin', 'supervisor', 'standard')
def get_call_logs():
    """Get filtered call logs"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    user_id = request.args.get('user_id')
    verification = request.args.get('verification')
    requested_agent = request.args.get('requested_agent')
    payment_agreed = request.args.get('payment_agreed')
    status = request.args.get('status')
    
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    
    try:
        query = '''
            SELECT cl.*, u.name as caller_name
            FROM call_logs cl
            LEFT JOIN users u ON cl.user_id = u.id
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            query += ' AND cl.call_start_time >= %s'
            params.append(start_date)
        if end_date:
            query += ' AND cl.call_start_time <= %s'
            params.append(end_date)
        if user_id:
            query += ' AND cl.user_id = %s'
            params.append(user_id)
        if verification:
            query += ' AND cl.verification_successful = %s'
            params.append(verification == 'true')
        if requested_agent:
            query += ' AND cl.requested_agent = %s'
            params.append(requested_agent == 'true')
        if payment_agreed:
            query += ' AND cl.agreed_to_payment_details = %s'
            params.append(payment_agreed == 'true')
        if status:
            query += ' AND cl.call_status = %s'
            params.append(status)
            
        query += ' ORDER BY cl.call_start_time DESC'
        cursor.execute(query, params)
        logs = cursor.fetchall()
        
        return jsonify({"call_logs": logs})
    finally:
        cursor.close()
        conn.close()

@app.route('/api/reports/call-logs/export', methods=['GET'])
@role_required('admin', 'supervisor')
def export_call_logs():
    """Export filtered call logs to CSV"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    user_id = request.args.get('user_id')
    verification = request.args.get('verification')
    requested_agent = request.args.get('requested_agent')
    payment_agreed = request.args.get('payment_agreed')
    status = request.args.get('status')
    
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    
    try:
        query = '''
            SELECT 
                u.name as caller_name,
                u.card_number,
                cl.call_start_time,
                cl.call_end_time,
                cl.call_duration,
                cl.verification_attempts,
                cl.verification_successful,
                cl.information_repeated,
                cl.requested_agent,
                cl.agreed_to_payment_details,
                cl.payment_method_interest,
                cl.follow_up_choice,
                cl.call_status,
                cl.notes
            FROM call_logs cl
            LEFT JOIN users u ON cl.user_id = u.id
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            query += ' AND cl.call_start_time >= %s'
            params.append(start_date)
        if end_date:
            query += ' AND cl.call_start_time <= %s'
            params.append(end_date)
        if user_id:
            query += ' AND cl.user_id = %s'
            params.append(user_id)
        if verification:
            query += ' AND cl.verification_successful = %s'
            params.append(verification == 'true')
        if requested_agent:
            query += ' AND cl.requested_agent = %s'
            params.append(requested_agent == 'true')
        if payment_agreed:
            query += ' AND cl.agreed_to_payment_details = %s'
            params.append(payment_agreed == 'true')
        if status:
            query += ' AND cl.call_status = %s'
            params.append(status)
            
        query += ' ORDER BY cl.call_start_time DESC'
        cursor.execute(query, params)
        logs = cursor.fetchall()
        
        # Create CSV file
        output = StringIO()
        writer = csv.DictWriter(output, fieldnames=logs[0].keys() if logs else [])
        writer.writeheader()
        writer.writerows(logs)
        
        # Log the export
        current_user_id = get_jwt_identity()
        log_audit(
            current_user_id,
            'export_call_logs',
            f"Exported {len(logs)} call logs to CSV",
            request.remote_addr
        )
        
        # Return CSV file
        return send_file(
            StringIO(output.getvalue()),
            mimetype='text/csv',
            as_attachment=True,
            download_name=f'call_logs_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        )
    finally:
        cursor.close()
        conn.close()

@app.route('/api/reports/analytics', methods=['GET'])
@role_required('admin', 'supervisor')
def get_analytics():
    """Get call analytics"""
    start_date = request.args.get('start_date', 
        (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', 
        datetime.now().strftime('%Y-%m-%d'))
    
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    
    try:
        # Get total calls
        cursor.execute('''
            SELECT COUNT(*) as total_calls,
                   AVG(call_duration) as avg_duration,
                   SUM(verification_successful) as successful_verifications,
                   SUM(requested_agent) as agent_requests,
                   SUM(agreed_to_payment_details) as payment_agreements
            FROM call_logs
            WHERE call_start_time BETWEEN %s AND %s
        ''', (start_date, end_date))
        overview = cursor.fetchone()
        
        # Get calls by status
        cursor.execute('''
            SELECT call_status, COUNT(*) as count
            FROM call_logs
            WHERE call_start_time BETWEEN %s AND %s
            GROUP BY call_status
        ''', (start_date, end_date))
        status_breakdown = cursor.fetchall()
        
        # Get calls by hour
        cursor.execute('''
            SELECT HOUR(call_start_time) as hour,
                   COUNT(*) as count
            FROM call_logs
            WHERE call_start_time BETWEEN %s AND %s
            GROUP BY HOUR(call_start_time)
            ORDER BY hour
        ''', (start_date, end_date))
        hourly_distribution = cursor.fetchall()
        
        # Get verification success rate trend
        cursor.execute('''
            SELECT DATE(call_start_time) as date,
                   COUNT(*) as total,
                   SUM(verification_successful) as successful
            FROM call_logs
            WHERE call_start_time BETWEEN %s AND %s
            GROUP BY DATE(call_start_time)
            ORDER BY date
        ''', (start_date, end_date))
        verification_trend = cursor.fetchall()
        
        return jsonify({
            "overview": overview,
            "status_breakdown": status_breakdown,
            "hourly_distribution": hourly_distribution,
            "verification_trend": verification_trend
        })
    finally:
        cursor.close()
        conn.close()

@app.route('/api/reports/audit-logs', methods=['GET'])
@role_required('admin', 'supervisor')
def get_audit_logs():
    """Get filtered audit logs"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    user_id = request.args.get('user_id')
    action = request.args.get('action')
    
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    
    try:
        query = '''
            SELECT al.*, u.username
            FROM audit_logs al
            JOIN system_users u ON al.user_id = u.id
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            query += ' AND al.created_at >= %s'
            params.append(start_date)
        if end_date:
            query += ' AND al.created_at <= %s'
            params.append(end_date)
        if user_id:
            query += ' AND al.user_id = %s'
            params.append(user_id)
        if action:
            query += ' AND al.action = %s'
            params.append(action)
            
        query += ' ORDER BY al.created_at DESC'
        cursor.execute(query, params)
        logs = cursor.fetchall()
        
        return jsonify({"audit_logs": logs})
    finally:
        cursor.close()
        conn.close()

@app.route('/api/reports/audit-logs/export', methods=['GET'])
@role_required('admin')
def export_audit_logs():
    """Export filtered audit logs to CSV"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    user_id = request.args.get('user_id')
    action = request.args.get('action')
    
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    
    try:
        query = '''
            SELECT 
                u.username,
                al.action,
                al.details,
                al.ip_address,
                al.created_at
            FROM audit_logs al
            JOIN system_users u ON al.user_id = u.id
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            query += ' AND al.created_at >= %s'
            params.append(start_date)
        if end_date:
            query += ' AND al.created_at <= %s'
            params.append(end_date)
        if user_id:
            query += ' AND al.user_id = %s'
            params.append(user_id)
        if action:
            query += ' AND al.action = %s'
            params.append(action)
            
        query += ' ORDER BY al.created_at DESC'
        cursor.execute(query, params)
        logs = cursor.fetchall()
        
        # Create CSV file
        output = StringIO()
        writer = csv.DictWriter(output, fieldnames=logs[0].keys() if logs else [])
        writer.writeheader()
        writer.writerows(logs)
        
        # Log the export
        current_user_id = get_jwt_identity()
        log_audit(
            current_user_id,
            'export_audit_logs',
            f"Exported {len(logs)} audit logs to CSV",
            request.remote_addr
        )
        
        # Return CSV file
        return send_file(
            StringIO(output.getvalue()),
            mimetype='text/csv',
            as_attachment=True,
            download_name=f'audit_logs_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        )
    finally:
        cursor.close()
        conn.close()
