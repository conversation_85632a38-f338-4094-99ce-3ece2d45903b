from flask import jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import app, MYSQL_CONFIG
from auth import log_audit
from rbac import role_required, require_login
import mysql.connector

# Prompt Management Routes
@app.route('/api/prompts', methods=['GET'])
@jwt_required()
def list_prompts():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        # Support filtering by section and language
        section = request.args.get('section')
        language_code = request.args.get('language_code')
        
        query = '''
            SELECT p.*, l.name as language_name, 
                   cu.username as created_by_user,
                   uu.username as updated_by_user
            FROM prompts p
            LEFT JOIN languages l ON p.language_code = l.code
            LEFT JOIN system_users cu ON p.created_by = cu.id
            LEFT JOIN system_users uu ON p.updated_by = uu.id
            WHERE 1=1
        '''
        params = []
        
        if section:
            query += ' AND p.section = %s'
            params.append(section)
        if language_code:
            query += ' AND p.language_code = %s'
            params.append(language_code)
            
        query += ' ORDER BY p.section, p.name'
        
        cursor.execute(query, params)
        prompts = cursor.fetchall()
        return jsonify({"prompts": prompts})
    finally:
        cursor.close()
        conn.close()

@app.route('/api/prompts', methods=['POST'])
@admin_required
def create_prompt():
    data = request.get_json()
    required_fields = ['section', 'name', 'content', 'language_code']
    
    if not all(field in data for field in required_fields):
        return jsonify({"msg": "Missing required fields"}), 400
    
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    try:
        current_user_id = get_jwt_identity()
        
        cursor.execute('''
            INSERT INTO prompts (
                section, name, content, language_code,
                is_active, is_toggle_enabled, created_by, updated_by
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        ''', (
            data['section'],
            data['name'],
            data['content'],
            data['language_code'],
            data.get('is_active', True),
            data.get('is_toggle_enabled', False),
            current_user_id,
            current_user_id
        ))
        conn.commit()
        prompt_id = cursor.lastrowid
        
        log_audit(
            current_user_id,
            'create_prompt',
            f"Created prompt {data['name']} in section {data['section']}",
            request.remote_addr
        )
        
        return jsonify({
            "msg": "Prompt created successfully",
            "prompt_id": prompt_id
        }), 201
    finally:
        cursor.close()
        conn.close()

@app.route('/api/prompts/<int:prompt_id>', methods=['PUT'])
@admin_required
def update_prompt(prompt_id):
    data = request.get_json()
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    try:
        current_user_id = get_jwt_identity()
        
        # Build dynamic update query based on provided fields
        updates = []
        values = []
        if 'content' in data:
            updates.append("content = %s")
            values.append(data['content'])
        if 'is_active' in data:
            updates.append("is_active = %s")
            values.append(data['is_active'])
        if 'is_toggle_enabled' in data:
            updates.append("is_toggle_enabled = %s")
            values.append(data['is_toggle_enabled'])
        if 'language_code' in data:
            updates.append("language_code = %s")
            values.append(data['language_code'])
            
        if updates:
            updates.append("updated_by = %s")
            values.append(current_user_id)
            values.append(prompt_id)
            
            query = f"UPDATE prompts SET {', '.join(updates)} WHERE id = %s"
            cursor.execute(query, values)
            conn.commit()
            
            log_audit(
                current_user_id,
                'update_prompt',
                f"Updated prompt {prompt_id}",
                request.remote_addr
            )
            
            return jsonify({"msg": "Prompt updated successfully"})
        return jsonify({"msg": "No changes made"}), 304
    finally:
        cursor.close()
        conn.close()

# Variable Management Routes
@app.route('/api/variables', methods=['GET'])
@jwt_required()
def list_variables():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute('''
            SELECT v.*, 
                   cu.username as created_by_user,
                   uu.username as updated_by_user
            FROM variables v
            LEFT JOIN system_users cu ON v.created_by = cu.id
            LEFT JOIN system_users uu ON v.updated_by = uu.id
            ORDER BY v.name
        ''')
        variables = cursor.fetchall()
        return jsonify({"variables": variables})
    finally:
        cursor.close()
        conn.close()

@app.route('/api/variables', methods=['POST'])
@admin_required
def create_variable():
    data = request.get_json()
    required_fields = ['name', 'data_type']
    
    if not all(field in data for field in required_fields):
        return jsonify({"msg": "Missing required fields"}), 400
    
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    try:
        current_user_id = get_jwt_identity()
        
        cursor.execute('''
            INSERT INTO variables (
                name, description, data_type, default_value,
                created_by, updated_by
            ) VALUES (%s, %s, %s, %s, %s, %s)
        ''', (
            data['name'],
            data.get('description', ''),
            data['data_type'],
            data.get('default_value'),
            current_user_id,
            current_user_id
        ))
        conn.commit()
        variable_id = cursor.lastrowid
        
        log_audit(
            current_user_id,
            'create_variable',
            f"Created variable {data['name']}",
            request.remote_addr
        )
        
        return jsonify({
            "msg": "Variable created successfully",
            "variable_id": variable_id
        }), 201
    finally:
        cursor.close()
        conn.close()

@app.route('/api/variables/<int:variable_id>', methods=['PUT'])
@admin_required
def update_variable(variable_id):
    data = request.get_json()
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    try:
        current_user_id = get_jwt_identity()
        
        # Build dynamic update query based on provided fields
        updates = []
        values = []
        if 'description' in data:
            updates.append("description = %s")
            values.append(data['description'])
        if 'data_type' in data:
            updates.append("data_type = %s")
            values.append(data['data_type'])
        if 'default_value' in data:
            updates.append("default_value = %s")
            values.append(data['default_value'])
            
        if updates:
            updates.append("updated_by = %s")
            values.append(current_user_id)
            values.append(variable_id)
            
            query = f"UPDATE variables SET {', '.join(updates)} WHERE id = %s"
            cursor.execute(query, values)
            conn.commit()
            
            log_audit(
                current_user_id,
                'update_variable',
                f"Updated variable {variable_id}",
                request.remote_addr
            )
            
            return jsonify({"msg": "Variable updated successfully"})
        return jsonify({"msg": "No changes made"}), 304
    finally:
        cursor.close()
        conn.close()
