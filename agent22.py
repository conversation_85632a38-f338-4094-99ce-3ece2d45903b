#!/usr/bin/env python3
"""
Robust AGI script: gTTS (with offline fallback) -> sox/ffmpeg conversion -> plays cached .ulaw/.wav
Saves sounds into /var/lib/asterisk/sounds/custom/
Includes SQLite DB logging (same schema as provided)
"""

import sys
import os
import sqlite3
import hashlib
import tempfile
import subprocess
import shutil
from gtts import gTTS
from datetime import datetime

# Paths
DB_PATH = "/var/lib/asterisk/agi-bin/assistant/example.db"
SOUNDS_SUBDIR = "custom"
SOUNDS_DIR = os.path.join("/var/lib/asterisk/sounds", SOUNDS_SUBDIR)
os.makedirs(SOUNDS_DIR, exist_ok=True)

# -------------
# AGI helpers
# -------------
def agi_read_env():
    env = {}
    while True:
        line = sys.stdin.readline()
        if not line:
            break
        line = line.strip()
        if line == "":
            break
        if ":" in line:
            k, v = line.split(":", 1)
            env[k.strip()] = v.strip()
    return env

def agi_cmd(cmd):
    """Send AGI command and return Asterisk response line (raw)."""
    sys.stdout.write(cmd.strip() + "\n")
    sys.stdout.flush()
    resp = sys.stdin.readline()
    if resp is None:
        return ""
    return resp.strip()

def agi_verbose(msg, level=1):
    """Log to Asterisk and stderr for debugging."""
    try:
        agi_cmd(f'VERBOSE "{msg}" {level}')
    except Exception:
        pass
    sys.stderr.write(f"[AGI VERBOSE] {msg}\n")
    sys.stderr.flush()

# -------------
# Utility helpers
# -------------
def cmd_exists(cmd):
    return shutil.which(cmd) is not None

def safe_run(cmd_list):
    """Run a command, return True on success, False on failure. Capture stderr to log."""
    try:
        subprocess.run(cmd_list, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True
    except subprocess.CalledProcessError as e:
        sys.stderr.write(f"[CMD FAIL] {' '.join(cmd_list)} => {e}\n")
        sys.stderr.flush()
        return False
    except Exception as e:
        sys.stderr.write(f"[CMD ERROR] {' '.join(cmd_list)} => {e}\n")
        sys.stderr.flush()
        return False

def text_to_hash_filename(text):
    h = hashlib.sha1(text.encode("utf-8")).hexdigest()
    return f"tts_{h}"

def file_ok(path, min_bytes=200):
    return os.path.exists(path) and os.path.getsize(path) > min_bytes

# -------------
# TTS & conversion
# -------------
def try_offline_tts(text, out_wav):
    """
    Try pico2wave or espeak to generate out_wav directly.
    Returns True on success.
    """
    if cmd_exists("pico2wave"):
        try:
            # pico2wave -w out.wav "text"
            proc = subprocess.run(["pico2wave", "-w", out_wav, text], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return file_ok(out_wav, min_bytes=200)
        except Exception as e:
            sys.stderr.write(f"[TTS pico2wave failed] {e}\n"); sys.stderr.flush()
    if cmd_exists("espeak"):
        try:
            # espeak -w out.wav "text"
            proc = subprocess.run(["espeak", "-w", out_wav, text], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return file_ok(out_wav, min_bytes=200)
        except Exception as e:
            sys.stderr.write(f"[TTS espeak failed] {e}\n"); sys.stderr.flush()
    return False

def ensure_tts_file(text, base_filename=None):
    """
    Ensure that a playable TTS file exists for `text`.
    Returns base_filename (no extension) on success, or None on failure.
    """
    if base_filename is None:
        base_filename = text_to_hash_filename(text)

    base_path = os.path.join(SOUNDS_DIR, base_filename)
    mp3_path = base_path + ".mp3"
    wav_path = base_path + ".wav"
    ulaw_path = base_path + ".ulaw"

    # If we already have ulaw or wav, reuse it
    if file_ok(ulaw_path):
        agi_verbose(f"Using cached ulaw: {ulaw_path}")
        return base_filename
    if file_ok(wav_path):
        agi_verbose(f"Using cached wav: {wav_path}")
        return base_filename

    # 1) Try gTTS (online)
    try:
        tts = gTTS(text=text, lang="en")
        tts.save(mp3_path)
        sys.stderr.write(f"[TTS] Generated mp3: {mp3_path}\n"); sys.stderr.flush()
        if not file_ok(mp3_path, min_bytes=100):
            # bad mp3 (likely no internet); remove and try offline
            try:
                os.remove(mp3_path)
            except:
                pass
            raise RuntimeError("gTTS produced empty/invalid mp3")
    except Exception as e:
        sys.stderr.write(f"[TTS WARN] gTTS failed: {e}\n"); sys.stderr.flush()
        # Try offline TTS directly into wav
        if try_offline_tts(text, wav_path):
            sys.stderr.write(f"[TTS] offline TTS produced wav: {wav_path}\n"); sys.stderr.flush()
            # convert wav -> ulaw
            if cmd_exists("sox"):
                if safe_run(["sox", wav_path, "-r", "8000", "-c", "1", "-t", "ul", ulaw_path]):
                    os.chmod(ulaw_path, 0o644)
                    return base_filename
            # if sox not available, keep wav
            if file_ok(wav_path):
                os.chmod(wav_path, 0o644)
                return base_filename
        # final fallback: gTTS failed and offline TTS failed
        return None

    # 2) Convert mp3 -> ulaw (preferred) using sox
    if cmd_exists("sox"):
        # try direct mp3 -> ulaw
        if safe_run(["sox", mp3_path, "-r", "8000", "-c", "1", "-t", "ul", ulaw_path]):
            try:
                os.chmod(ulaw_path, 0o644)
            except:
                pass
            # remove mp3
            try: os.remove(mp3_path)
            except: pass
            return base_filename
        else:
            sys.stderr.write("[TTS] sox direct mp3->ulaw failed; trying mp3->wav then wav->ulaw\n")
            # try mp3->wav then wav->ulaw
            if safe_run(["sox", mp3_path, "-r", "8000", "-c", "1", wav_path]):
                if safe_run(["sox", wav_path, "-r", "8000", "-c", "1", "-t", "ul", ulaw_path]):
                    try:
                        os.chmod(ulaw_path, 0o644)
                    except:
                        pass
                    try:
                        os.remove(mp3_path)
                    except:
                        pass
                    try:
                        os.remove(wav_path)
                    except:
                        pass
                    return base_filename
    # 3) Try ffmpeg fallback (if available)
    if cmd_exists("ffmpeg"):
        # ffmpeg -y -i in.mp3 -ar 8000 -ac 1 -f mulaw out.ulaw
        if safe_run(["ffmpeg", "-y", "-i", mp3_path, "-ar", "8000", "-ac", "1", "-f", "mulaw", ulaw_path]):
            try:
                os.chmod(ulaw_path, 0o644)
            except:
                pass
            try:
                os.remove(mp3_path)
            except:
                pass
            return base_filename
        # try ffmpeg -> wav
        if safe_run(["ffmpeg", "-y", "-i", mp3_path, "-ar", "8000", "-ac", "1", wav_path]):
            try:
                os.chmod(wav_path, 0o644)
            except:
                pass
            try:
                os.remove(mp3_path)
            except:
                pass
            return base_filename

    # If conversion failed but wav exists (maybe from earlier attempt), return wav
    if file_ok(wav_path):
        try:
            os.chmod(wav_path, 0o644)
        except:
            pass
        try:
            if os.path.exists(mp3_path):
                os.remove(mp3_path)
        except:
            pass
        return base_filename

    # nothing worked
    try:
        if os.path.exists(mp3_path):
            os.remove(mp3_path)
    except:
        pass
    return None

def speak(text, filename=None):
    """Ensure TTS exists then STREAM FILE to caller (falls back to beep on failure)."""
    if filename is None:
        filename = text_to_hash_filename(text)

    base = ensure_tts_file(text, filename)
    if not base:
        agi_verbose("TTS generation failed, using fallback.")
        # fallback: short beep so caller doesn't wait
        agi_cmd('STREAM FILE "beep" ""')
        return

    # stream file under sounds/custom/<base> (omit extension)
    target = f"{SOUNDS_SUBDIR}/{base}"
    agi_verbose(f"STREAM FILE {target}")
    agi_cmd(f'STREAM FILE {target} ""')

def get_digits(prompt_text, max_digits=6, timeout_ms=10000, tts_filename=None):
    """
    TTS prompt (cached) + GET DATA. Returns digit string or empty string.
    """
    if tts_filename is None:
        tts_filename = text_to_hash_filename(prompt_text)

    # ensure TTS exists (or fallback)
    base = ensure_tts_file(prompt_text, tts_filename)
    if base:
        target = f"{SOUNDS_SUBDIR}/{base}"
    else:
        # fallback to beep
        target = "beep"
        agi_verbose("GET DATA will use beep fallback (no TTS).")

    agi_verbose(f"GET DATA {target} {timeout_ms} {max_digits}")
    resp = agi_cmd(f"GET DATA {target} {timeout_ms} {max_digits}")
    sys.stderr.write(f"[AGI] GET DATA response: {resp}\n"); sys.stderr.flush()

    if not resp:
        return ""
    if "result=" in resp:
        try:
            val = resp.split("result=", 1)[1].split()[0]
            if val.isdigit():
                return val
            else:
                return ""
        except Exception:
            return ""
    return ""

# -------------
# Database helpers (unchanged from original)
# -------------
def setup_database():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            balance REAL,
            debt REAL,
            discount REAL,
            birthday TEXT,
            card_number TEXT,
            due_date TEXT,
            discount_due_date TEXT
        )
    ''')
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS call_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            caller_name TEXT,
            card_number TEXT,
            call_start_time DATETIME,
            call_end_time DATETIME,
            call_duration INTEGER,
            verification_attempts INTEGER,
            verification_successful BOOLEAN,
            information_repeated BOOLEAN,
            requested_agent BOOLEAN,
            agreed_to_payment_details BOOLEAN,
            payment_method_interest TEXT,
            follow_up_choice TEXT,
            call_status TEXT,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    conn.commit()
    conn.close()

def log_call_interaction(user_id, caller_name, card_number, call_start_time, call_end_time,
                        verification_attempts, verification_successful, information_repeated,
                        requested_agent, agreed_to_payment_details, payment_method_interest,
                        follow_up_choice, call_status, notes=""):
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        call_duration = 0
        if call_start_time and call_end_time:
            start = datetime.fromisoformat(call_start_time)
            end = datetime.fromisoformat(call_end_time)
            call_duration = int((end - start).total_seconds())
        cursor.execute('''
            INSERT INTO call_logs (user_id, caller_name, card_number, call_start_time, call_end_time,
                                  call_duration, verification_attempts, verification_successful,
                                  information_repeated, requested_agent, agreed_to_payment_details,
                                  payment_method_interest, follow_up_choice, call_status, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, caller_name, card_number, call_start_time, call_end_time, call_duration,
              verification_attempts, verification_successful, information_repeated, requested_agent,
              agreed_to_payment_details, payment_method_interest, follow_up_choice, call_status, notes))
        conn.commit()
    except Exception as e:
        sys.stderr.write(f"[DB LOG ERROR] {e}\n")
        sys.stderr.flush()
    finally:
        try:
            conn.close()
        except:
            pass

# -------------
# Main AGI flow (keeps your previous logic)
# -------------
def main():
    agi_env = agi_read_env()
    agi_verbose("AGI environment read.")

    setup_database()
    agi_cmd("ANSWER")

    company_name = "Alexis A Molaer Law Offices"

    call_start_time = datetime.now().isoformat()
    verification_attempts = 0
    verification_successful = False
    information_repeated = False
    requested_agent = False
    agreed_to_payment_details = False
    payment_method_interest = ""
    follow_up_choice = ""
    call_status = "in_progress"
    caller_name = ""
    card_number = ""
    user = None
    user_id = None

    # Welcome
    speak(f"Thank you for calling {company_name}. You are connected to our automated Service Desk Assistant.", filename="welcome")

    # Verification loop
    while verification_attempts < 3:
        verification_attempts += 1
        card_number = get_digits("Please enter your card number followed by the pound key.", max_digits=10, timeout_ms=15000, tts_filename="prompt_card")
        if not card_number:
            speak("I did not receive your card number. Let's try again.", filename="no_input")
            continue

        birthyear = get_digits("For verification please enter your four digit birth year.", max_digits=4, timeout_ms=15000, tts_filename="prompt_birthyear")
        if not birthyear:
            speak("I did not receive your birth year. Let's try again.", filename="no_input")
            continue

        # Lookup user
        try:
            conn = sqlite3.connect(DB_PATH)
            conn.row_factory = sqlite3.Row
            cur = conn.cursor()
            cur.execute("SELECT * FROM users WHERE card_number = ?", (card_number,))
            user = cur.fetchone()
            conn.close()
        except Exception as e:
            sys.stderr.write(f"[DB ERROR] {e}\n"); sys.stderr.flush()
            user = None

        if user:
            try:
                db_year = user["birthday"].split("-")[0] if user["birthday"] else ""
            except Exception:
                db_year = ""
            if birthyear == db_year:
                verification_successful = True
                user_id = user["id"]
                caller_name = user["name"]
                speak("Thank you. I have verified your account.", filename="verified")
                break
            else:
                speak("The details you entered do not match our records. Please try again.", filename="mismatch")
        else:
            speak("We could not find a record for that card number. Please try again.", filename="no_record")

    if not verification_successful:
        speak("I cannot verify your account after multiple attempts. Please call back during business hours.", filename="failed_verification")
        call_status = "failed_verification"
    else:
        first_name = user["name"].split()[0] if user["name"] else "Customer"
        speak(f"Hello {first_name}. Your current balance is {user['balance']} pesos.", filename="balance")
        speak(f"Your payment is due on {user['due_date']}.", filename="due_date")
        speak(f"You may avail a discount of {user['discount']} pesos if paid before {user['discount_due_date']}.", filename="discount")
        speak("You may pay via Bank Transfer, GCash, Maya, or at our partner payment centers.", filename="payment_options")

        choice = get_digits("Press 1 to repeat information. Press 0 to speak to an agent. Press 2 to have payment details sent to your registered mobile number.", max_digits=1, timeout_ms=15000, tts_filename="followup_menu")
        if choice == "1":
            information_repeated = True
            follow_up_choice = "repeat_info"
            speak(f"Repeating: your current balance is {user['balance']} pesos. Due date is {user['due_date']}. Discount {user['discount']} until {user['discount_due_date']}.", filename="repeat_info")
            call_status = "completed"
        elif choice == "0":
            requested_agent = True
            follow_up_choice = "agent_transfer"
            call_status = "transferred"
            speak("Please wait while I connect you to a representative.", filename="transfer")
            # Note: To perform Dial/Queue transfer, handle in dialplan after AGI returns.
        elif choice == "2":
            agreed_to_payment_details = True
            follow_up_choice = "payment_details"
            call_status = "completed"
            speak("Payment details will be sent to your registered mobile number.", filename="send_payment")
        else:
            agreed_to_payment_details = True
            follow_up_choice = "no_choice"
            call_status = "completed"
            speak("Thank you. Have a great day.", filename="goodbye")

    # Log call
    call_end_time = datetime.now().isoformat()
    if not caller_name:
        caller_name = "Unknown Caller"
    log_call_interaction(
        user_id=user_id,
        caller_name=caller_name,
        card_number=card_number,
        call_start_time=call_start_time,
        call_end_time=call_end_time,
        verification_attempts=verification_attempts,
        verification_successful=verification_successful,
        information_repeated=information_repeated,
        requested_agent=requested_agent,
        agreed_to_payment_details=agreed_to_payment_details,
        payment_method_interest=payment_method_interest,
        follow_up_choice=follow_up_choice,
        call_status=call_status,
        notes="Automated AGI call"
    )
    sys.stderr.write(f"[AGI] Call logged. Status: {call_status}\n"); sys.stderr.flush()

    # Hang up
    agi_cmd("HANGUP")
    sys.exit(0)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        sys.stderr.write(f"[AGI FATAL] {e}\n"); sys.stderr.flush()
        try:
            agi_cmd(f'VERBOSE "AGI fatal error: {e}" 1')
        except:
            pass
        sys.exit(1)

