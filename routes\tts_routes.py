from flask import jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import app, MYSQL_CONFIG
from auth import admin_required, supervisor_required, log_audit
import mysql.connector
import json

# TTS Settings Routes
@app.route('/api/tts-settings', methods=['GET'])
@jwt_required()
def list_tts_settings():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute('''
            SELECT ts.*, 
                   cu.username as created_by_user,
                   uu.username as updated_by_user
            FROM tts_settings ts
            LEFT JOIN system_users cu ON ts.created_by = cu.id
            LEFT JOIN system_users uu ON ts.updated_by = uu.id
            ORDER BY ts.engine
        ''')
        settings = cursor.fetchall()
        
        # Parse JSON settings field
        for setting in settings:
            if setting['settings']:
                setting['settings'] = json.loads(setting['settings'])
        
        return jsonify({"tts_settings": settings})
    finally:
        cursor.close()
        conn.close()

@app.route('/api/tts-settings', methods=['POST'])
@admin_required
def create_tts_setting():
    data = request.get_json()
    required_fields = ['engine', 'settings']
    
    if not all(field in data for field in required_fields):
        return jsonify({"msg": "Missing required fields"}), 400
    
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    try:
        current_user_id = get_jwt_identity()
        
        cursor.execute('''
            INSERT INTO tts_settings (
                engine, api_key, settings, is_active,
                created_by, updated_by
            ) VALUES (%s, %s, %s, %s, %s, %s)
        ''', (
            data['engine'],
            data.get('api_key'),
            json.dumps(data['settings']),
            data.get('is_active', True),
            current_user_id,
            current_user_id
        ))
        conn.commit()
        setting_id = cursor.lastrowid
        
        log_audit(
            current_user_id,
            'create_tts_setting',
            f"Created TTS setting for engine {data['engine']}",
            request.remote_addr
        )
        
        return jsonify({
            "msg": "TTS setting created successfully",
            "setting_id": setting_id
        }), 201
    finally:
        cursor.close()
        conn.close()

@app.route('/api/tts-settings/<int:setting_id>', methods=['PUT'])
@admin_required
def update_tts_setting(setting_id):
    data = request.get_json()
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    try:
        current_user_id = get_jwt_identity()
        
        # Build dynamic update query based on provided fields
        updates = []
        values = []
        if 'api_key' in data:
            updates.append("api_key = %s")
            values.append(data['api_key'])
        if 'settings' in data:
            updates.append("settings = %s")
            values.append(json.dumps(data['settings']))
        if 'is_active' in data:
            updates.append("is_active = %s")
            values.append(data['is_active'])
            
        if updates:
            updates.append("updated_by = %s")
            values.append(current_user_id)
            values.append(setting_id)
            
            query = f"UPDATE tts_settings SET {', '.join(updates)} WHERE id = %s"
            cursor.execute(query, values)
            conn.commit()
            
            log_audit(
                current_user_id,
                'update_tts_setting',
                f"Updated TTS setting {setting_id}",
                request.remote_addr
            )
            
            return jsonify({"msg": "TTS setting updated successfully"})
        return jsonify({"msg": "No changes made"}), 304
    finally:
        cursor.close()
        conn.close()

# Language Management Routes
@app.route('/api/languages', methods=['GET'])
@jwt_required()
def list_languages():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute('''
            SELECT * FROM languages
            ORDER BY name
        ''')
        languages = cursor.fetchall()
        return jsonify({"languages": languages})
    finally:
        cursor.close()
        conn.close()

@app.route('/api/languages', methods=['POST'])
@admin_required
def create_language():
    data = request.get_json()
    required_fields = ['code', 'name']
    
    if not all(field in data for field in required_fields):
        return jsonify({"msg": "Missing required fields"}), 400
    
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    try:
        cursor.execute('''
            INSERT INTO languages (code, name, is_active)
            VALUES (%s, %s, %s)
        ''', (
            data['code'],
            data['name'],
            data.get('is_active', True)
        ))
        conn.commit()
        language_id = cursor.lastrowid
        
        current_user_id = get_jwt_identity()
        log_audit(
            current_user_id,
            'create_language',
            f"Created language {data['name']} ({data['code']})",
            request.remote_addr
        )
        
        return jsonify({
            "msg": "Language created successfully",
            "language_id": language_id
        }), 201
    finally:
        cursor.close()
        conn.close()

@app.route('/api/languages/<int:language_id>', methods=['PUT'])
@admin_required
def update_language(language_id):
    data = request.get_json()
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    try:
        # Build dynamic update query based on provided fields
        updates = []
        values = []
        if 'name' in data:
            updates.append("name = %s")
            values.append(data['name'])
        if 'is_active' in data:
            updates.append("is_active = %s")
            values.append(data['is_active'])
            
        if updates:
            values.append(language_id)
            query = f"UPDATE languages SET {', '.join(updates)} WHERE id = %s"
            cursor.execute(query, values)
            conn.commit()
            
            current_user_id = get_jwt_identity()
            log_audit(
                current_user_id,
                'update_language',
                f"Updated language {language_id}",
                request.remote_addr
            )
            
            return jsonify({"msg": "Language updated successfully"})
        return jsonify({"msg": "No changes made"}), 304
    finally:
        cursor.close()
        conn.close()
