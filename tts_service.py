import os
from abc import ABC, abstractmethod
from google.cloud import texttospeech
import boto3
from gtts import gTTS
import tempfile
import json
from typing import Dict, Any
from mysql.connector import MySQLConnection
from app import MYSQL_CONFIG

class TTSEngine(ABC):
    @abstractmethod
    def synthesize(self, text: str, settings: Dict[str, Any]) -> bytes:
        pass

class GoogleTTS(TTSEngine):
    def __init__(self, api_key: str):
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = api_key
        self.client = texttospeech.TextToSpeechClient()
    
    def synthesize(self, text: str, settings: Dict[str, Any]) -> bytes:
        synthesis_input = texttospeech.SynthesisInput(text=text)
        
        voice = texttospeech.VoiceSelectionParams(
            language_code=settings.get('language_code', 'en-US'),
            name=settings.get('voice', 'en-US-Standard-A')
        )
        
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3,
            speaking_rate=float(settings.get('rate', 1.0)),
            pitch=float(settings.get('pitch', 0.0))
        )
        
        response = self.client.synthesize_speech(
            input=synthesis_input,
            voice=voice,
            audio_config=audio_config
        )
        
        return response.audio_content

class AmazonTTS(TTSEngine):
    def __init__(self, access_key: str, secret_key: str, region: str):
        self.client = boto3.client(
            'polly',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region
        )
    
    def synthesize(self, text: str, settings: Dict[str, Any]) -> bytes:
        response = self.client.synthesize_speech(
            Engine=settings.get('engine', 'standard'),
            LanguageCode=settings.get('language_code', 'en-US'),
            OutputFormat='mp3',
            Text=text,
            VoiceId=settings.get('voice', 'Joanna'),
            TextType='text'
        )
        
        return response['AudioStream'].read()

class LocalTTS(TTSEngine):
    def synthesize(self, text: str, settings: Dict[str, Any]) -> bytes:
        tts = gTTS(
            text=text,
            lang=settings.get('language_code', 'en'),
            slow=settings.get('slow', False)
        )
        
        with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
            tts.save(temp_file.name)
            with open(temp_file.name, 'rb') as f:
                audio_content = f.read()
            os.unlink(temp_file.name)
            
        return audio_content

class TTSService:
    def __init__(self):
        self._engine = None
        self._settings = None
        self._load_settings()
    
    def _load_settings(self):
        """Load TTS settings from database"""
        conn = MySQLConnection(**MYSQL_CONFIG)
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute('''
                SELECT * FROM tts_settings 
                WHERE is_active = TRUE 
                ORDER BY id DESC LIMIT 1
            ''')
            result = cursor.fetchone()
            
            if result:
                self._settings = json.loads(result['settings'])
                engine_type = result['engine']
                
                if engine_type == 'google':
                    self._engine = GoogleTTS(self._settings['apiKey'])
                elif engine_type == 'aws':
                    self._engine = AmazonTTS(
                        self._settings['accessKey'],
                        self._settings['secretKey'],
                        self._settings['region']
                    )
                else:
                    self._engine = LocalTTS()
            else:
                # Default to Local TTS if no settings found
                self._engine = LocalTTS()
                self._settings = {}
        finally:
            cursor.close()
            conn.close()
    
    def process_variables(self, text: str, variables: Dict[str, str]) -> str:
        """Replace variable placeholders with actual values"""
        for name, value in variables.items():
            text = text.replace(f"{{{name}}}", str(value))
        return text
    
    def synthesize(self, text: str, variables: Dict[str, str] = None) -> bytes:
        """Synthesize text to speech, replacing variables if provided"""
        if variables:
            text = self.process_variables(text, variables)
        
        # Reload settings in case they've changed
        self._load_settings()
        
        return self._engine.synthesize(text, self._settings)
    
    def preview(self, text: str, engine_type: str = None, settings: Dict[str, Any] = None) -> bytes:
        """Preview text with specific engine and settings without saving them"""
        if engine_type and settings:
            if engine_type == 'google':
                engine = GoogleTTS(settings['apiKey'])
            elif engine_type == 'aws':
                engine = AmazonTTS(
                    settings['accessKey'],
                    settings['secretKey'],
                    settings['region']
                )
            else:
                engine = LocalTTS()
            
            return engine.synthesize(text, settings)
        
        # Use current engine if no specific engine/settings provided
        return self.synthesize(text)
