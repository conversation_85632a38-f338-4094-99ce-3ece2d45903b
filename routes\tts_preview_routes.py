from flask import send_file, Response
from io import Bytes<PERSON>
from app import app
from tts_service import TTSService
from flask_jwt_extended import jwt_required, get_jwt_identity
from auth import log_audit
import mysql.connector
from app import MYSQL_CONFIG

tts_service = TTSService()

@app.route('/api/tts/preview', methods=['POST'])
@jwt_required()
def preview_tts():
    data = request.get_json()
    if not data or 'text' not in data:
        return jsonify({"msg": "Missing text"}), 400
    
    try:
        # Generate audio
        audio_content = tts_service.preview(
            data['text'],
            data.get('engine'),
            data.get('settings')
        )
        
        # Log the preview
        current_user_id = get_jwt_identity()
        log_audit(
            current_user_id,
            'preview_tts',
            f"Previewed TTS with engine {data.get('engine', 'default')}",
            request.remote_addr
        )
        
        # Return audio file
        return send_file(
            BytesIO(audio_content),
            mimetype='audio/mp3',
            as_attachment=True,
            download_name='preview.mp3'
        )
    except Exception as e:
        return jsonify({"msg": f"TTS preview failed: {str(e)}"}), 500

@app.route('/api/tts/synthesize', methods=['POST'])
@jwt_required()
def synthesize_tts():
    data = request.get_json()
    if not data or 'text' not in data:
        return jsonify({"msg": "Missing text"}), 400
    
    try:
        # Get variables if provided
        variables = data.get('variables', {})
        
        # Generate audio
        audio_content = tts_service.synthesize(data['text'], variables)
        
        # Log the synthesis
        current_user_id = get_jwt_identity()
        log_audit(
            current_user_id,
            'synthesize_tts',
            f"Synthesized TTS with {len(variables)} variables",
            request.remote_addr
        )
        
        # Return audio file
        return send_file(
            BytesIO(audio_content),
            mimetype='audio/mp3',
            as_attachment=True,
            download_name='speech.mp3'
        )
    except Exception as e:
        return jsonify({"msg": f"TTS synthesis failed: {str(e)}"}), 500

@app.route('/api/prompts/preview/<int:prompt_id>', methods=['GET'])
@jwt_required()
def preview_prompt(prompt_id):
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        # Get prompt content
        cursor.execute('''
            SELECT content FROM prompts WHERE id = %s
        ''', (prompt_id,))
        prompt = cursor.fetchone()
        
        if not prompt:
            return jsonify({"msg": "Prompt not found"}), 404
        
        # Get sample data for variables
        cursor.execute('SELECT name, default_value FROM variables')
        variables = {v['name']: v['default_value'] for v in cursor.fetchall()}
        
        # Generate audio with sample data
        audio_content = tts_service.synthesize(prompt['content'], variables)
        
        # Log the preview
        current_user_id = get_jwt_identity()
        log_audit(
            current_user_id,
            'preview_prompt',
            f"Previewed prompt {prompt_id}",
            request.remote_addr
        )
        
        # Return audio file
        return send_file(
            BytesIO(audio_content),
            mimetype='audio/mp3',
            as_attachment=True,
            download_name=f'prompt_{prompt_id}.mp3'
        )
    except Exception as e:
        return jsonify({"msg": f"Prompt preview failed: {str(e)}"}), 500
    finally:
        cursor.close()
        conn.close()
