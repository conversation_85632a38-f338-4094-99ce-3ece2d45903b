{% extends "base.html" %}

{% block title %}Call Logs{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1><i class="fas fa-phone"></i> Call Logs</h1>
                <p class="lead">Detailed call interaction history</p>
            </div>
            <div>
                <a href="{{ url_for('add_call_log') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Call Log
                </a>
                <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                    <i class="fas fa-chart-bar"></i> Back to Reports
                </a>
                <form method="POST" action="{{ url_for('delete_all_call_logs') }}" style="display:inline;">
                    <button type="submit" class="btn btn-danger ms-2" onclick="return confirm('Are you sure you want to delete ALL call logs?');">
                        <i class="fas fa-trash"></i> Delete All
                    </button>
                </form>
            </div>
        </div>
        <div class="mb-3 d-flex justify-content-end">
            <input type="text" id="searchInput" class="form-control w-auto me-2" placeholder="Search call logs...">
            <button class="btn btn-outline-primary" onclick="searchCallLogs()">
                <i class="fas fa-search"></i> Search
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> All Call Logs ({{ logs|length }})</h5>
            </div>
            <div class="card-body">
                {% if logs %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="callLogsTable">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Date/Time</th>
                                <th>Caller Name</th>
                                <th>Card Number</th>
                                <th>User Account</th>
                                <th>Duration</th>
                                <th>Verification</th>
                                <th>Payment Agreed</th>
                                <th>Agent Requested</th>
                                <th>Follow-up Choice</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs %}
                            <tr>
                                <td>{{ log.id }}</td>
                                <td>
                                    {% if log.call_start_time %}
                                        {{ log.call_start_time[:16] }}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>{{ log.caller_name }}</td>
                                <td>{{ log.card_number }}</td>
                                <td>
                                    {% if log.user_name %}
                                        {{ log.user_name }}
                                        <br><small class="text-muted">Balance: ₱{{ "%.2f"|format(log.balance) }}</small>
                                    {% else %}
                                        <span class="text-muted">Unknown</span>
                                    {% endif %}
                                </td>
                                <td>{{ log.call_duration }}s</td>
                                <td>
                                    {% if log.verification_successful %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Success
                                        </span>
                                        <br><small class="text-muted">{{ log.verification_attempts }} attempts</small>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times"></i> Failed
                                        </span>
                                        <br><small class="text-muted">{{ log.verification_attempts }} attempts</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if log.agreed_to_payment_details %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Yes
                                        </span>
                                        {% if log.payment_method_interest %}
                                            <br><small class="text-muted">{{ log.payment_method_interest }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times"></i> No
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if log.requested_agent %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-user-tie"></i> Yes
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">No</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if log.follow_up_choice %}
                                        <span class="badge bg-info">{{ log.follow_up_choice }}</span>
                                    {% else %}
                                        <span class="text-muted">None</span>
                                    {% endif %}
                                    {% if log.information_repeated %}
                                        <br><small class="badge bg-warning">Info Repeated</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ log.call_status }}</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#detailModal{{ log.id }}">
                                        <i class="fas fa-eye"></i> Details
                                    </button>
                                    <form method="POST" action="{{ url_for('delete_call_log', log_id=log.id) }}" style="display:inline;">
                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this call log?');">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Detail Modals -->
                {% for log in logs %}
                <div class="modal fade" id="detailModal{{ log.id }}" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Call Log Details - ID: {{ log.id }}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Call Information</h6>
                                        <table class="table table-sm">
                                            <tr><td><strong>Caller Name:</strong></td><td>{{ log.caller_name }}</td></tr>
                                            <tr><td><strong>Card Number:</strong></td><td>{{ log.card_number }}</td></tr>
                                            <tr><td><strong>Start Time:</strong></td><td>{{ log.call_start_time or 'N/A' }}</td></tr>
                                            <tr><td><strong>End Time:</strong></td><td>{{ log.call_end_time or 'N/A' }}</td></tr>
                                            <tr><td><strong>Duration:</strong></td><td>{{ log.call_duration }} seconds</td></tr>
                                            <tr><td><strong>Status:</strong></td><td>{{ log.call_status }}</td></tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Interaction Details</h6>
                                        <table class="table table-sm">
                                            <tr><td><strong>Verification Attempts:</strong></td><td>{{ log.verification_attempts }}</td></tr>
                                            <tr><td><strong>Verification Successful:</strong></td><td>{{ 'Yes' if log.verification_successful else 'No' }}</td></tr>
                                            <tr><td><strong>Information Repeated:</strong></td><td>{{ 'Yes' if log.information_repeated else 'No' }}</td></tr>
                                            <tr><td><strong>Requested Agent:</strong></td><td>{{ 'Yes' if log.requested_agent else 'No' }}</td></tr>
                                            <tr><td><strong>Agreed to Payment:</strong></td><td>{{ 'Yes' if log.agreed_to_payment_details else 'No' }}</td></tr>
                                            <tr><td><strong>Payment Method Interest:</strong></td><td>{{ log.payment_method_interest or 'None' }}</td></tr>
                                            <tr><td><strong>Follow-up Choice:</strong></td><td>{{ log.follow_up_choice or 'None' }}</td></tr>
                                        </table>
                                    </div>
                                </div>
                                {% if log.user_name %}
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <h6>Associated User Account</h6>
                                        <table class="table table-sm">
                                            <tr><td><strong>Name:</strong></td><td>{{ log.user_name }}</td></tr>
                                            <tr><td><strong>Balance:</strong></td><td>₱{{ "%.2f"|format(log.balance) }}</td></tr>
                                            <tr><td><strong>Debt:</strong></td><td>₱{{ "%.2f"|format(log.debt) }}</td></tr>
                                            <tr><td><strong>Discount:</strong></td><td>₱{{ "%.2f"|format(log.discount) }}</td></tr>
                                        </table>
                                    </div>
                                </div>
                                {% endif %}
                                {% if log.notes %}
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <h6>Notes</h6>
                                        <p class="border p-2 bg-light">{{ log.notes }}</p>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-phone fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No call logs found</h5>
                    <p class="text-muted">Call logs will appear here once the agent system starts logging interactions.</p>
                    <a href="{{ url_for('add_call_log') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Test Call Log
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function searchCallLogs() {
    var input = document.getElementById('searchInput').value.toLowerCase();
    var table = document.getElementById('callLogsTable');
    var rows = table.getElementsByTagName('tr');
    for (var i = 1; i < rows.length; i++) {
        var rowText = rows[i].innerText.toLowerCase();
        rows[i].style.display = rowText.includes(input) ? '' : 'none';
    }
}
</script>
{% endblock %}
