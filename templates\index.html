{% extends "base.html" %}

{% block title %}Caller Management System - Home{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h1><i class="fas fa-phone"></i> Caller Management System</h1>
        <p class="lead">Manage callers and track call interactions for <PERSON> A Molaer Law Offices</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-upload"></i> Upload Callers</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('upload_csv') }}" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".csv" required>
                        <div class="form-text">
                            CSV format: name, balance, debt, discount, birthday, card_number, due_date, discount_due_date
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload CSV
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cogs"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <a href="{{ url_for('add_caller_form') }}" class="btn btn-success mb-2">
                    <i class="fas fa-user-plus"></i> Add Single Caller
                </a>
                <br>
                <a href="{{ url_for('reports') }}" class="btn btn-info mb-2">
                    <i class="fas fa-chart-bar"></i> View Reports
                </a>
                <br>
                <a href="{{ url_for('call_logs') }}" class="btn btn-secondary mb-2">
                    <i class="fas fa-phone"></i> View Call Logs
                </a>
                <br>
                <form action="{{ url_for('clear_callers') }}" method="post" style="display: inline;">
                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to clear all callers?')">
                        <i class="fas fa-trash"></i> Clear All Callers
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list"></i> Current Callers ({{ users|length }})</h5>
                <span class="badge bg-primary">{{ users|length }} callers</span>
            </div>
            <div class="card-body">
                {% if users %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Balance</th>
                                <th>Debt</th>
                                <th>Discount</th>
                                <th>Birthday</th>
                                <th>Card Number</th>
                                <th>Due Date</th>
                                <th>Discount Due</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ user.id }}</td>
                                <td>{{ user.name }}</td>
                                <td>₱{{ "%.2f"|format(user.balance) }}</td>
                                <td>₱{{ "%.2f"|format(user.debt) }}</td>
                                <td>₱{{ "%.2f"|format(user.discount) }}</td>
                                <td>{{ user.birthday }}</td>
                                <td>{{ user.card_number }}</td>
                                <td>{{ user.due_date }}</td>
                                <td>{{ user.discount_due_date }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No callers found</h5>
                    <p class="text-muted">Upload a CSV file or add callers manually to get started.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
