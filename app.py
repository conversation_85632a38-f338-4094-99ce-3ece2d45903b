from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_user, logout_user, login_required, current_user
import mysql.connector
import csv
import io
from datetime import datetime, timedelta
from config import app, MYSQL_CONFIG, bcrypt, login_manager
from auth import admin_required, supervisor_required, log_audit
from models import User
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        if request.is_json:
            # Handle API login
            data = request.get_json()
            username = data.get('username')
            password = data.get('password')
        else:
            # Handle form login
            username = request.form.get('username')
            password = request.form.get('password')

        user = User.get_by_username(username)
        
        if user and user.password == password:  # Plain text comparison since we're not using hashed passwords
            login_user(user)
            log_audit(user.id, 'login', f'User {username} logged in')
            
            if request.is_json:
                return jsonify({
                    'status': 'success',
                    'message': 'Login successful',
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'role': user.role_name
                    }
                })
            return redirect(url_for('index'))
        
        if request.is_json:
            return jsonify({
                'status': 'error',
                'message': 'Invalid username or password'
            }), 401
            
        flash('Invalid username or password', 'error')
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    if current_user.is_authenticated:
        log_audit(current_user.id, 'logout', f'User {current_user.username} logged out')
    logout_user()
    if request.is_json:
        return jsonify({
            'status': 'success',
            'message': 'Logged out successfully'
        })
    return redirect(url_for('login'))

@app.route('/api/auth/login', methods=['POST'])
def api_login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    user = User.get_by_username(username)
    
    if user and user.password == password:  # Plain text comparison
        login_user(user)
        log_audit(user.id, 'login', f'User {username} logged in via API')
        return jsonify({
            'status': 'success',
            'message': 'Login successful',
            'user': {
                'id': user.id,
                'username': user.username,
                'role': user.role_name
            }
        })
    
    return jsonify({
        'status': 'error',
        'message': 'Invalid username or password'
    }), 401

@app.route('/api/auth/logout')
@login_required
def api_logout():
    if current_user.is_authenticated:
        log_audit(current_user.id, 'logout', f'User {current_user.username} logged out via API')
    logout_user()
    return jsonify({
        'status': 'success',
        'message': 'Logged out successfully'
    })

def init_database():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    # Create roles table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) UNIQUE,
            description TEXT,
            created_at DATETIME
        ) ENGINE=InnoDB
    ''')
    
    # Create system users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE,
            password VARCHAR(255),
            role_id INT,
            is_active BOOLEAN DEFAULT TRUE,
            last_login DATETIME,
            created_at DATETIME,
            FOREIGN KEY (role_id) REFERENCES roles(id)
        ) ENGINE=InnoDB
    ''')
    
    # Create audit logs table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS audit_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(255),
            details TEXT,
            ip_address VARCHAR(50),
            created_at DATETIME,
            FOREIGN KEY (user_id) REFERENCES users(id)
        ) ENGINE=InnoDB
    ''')
    
    # Create prompts table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS prompts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            section VARCHAR(50),
            name VARCHAR(255),
            content TEXT,
            language_code VARCHAR(10),
            is_active BOOLEAN DEFAULT TRUE,
            is_toggle_enabled BOOLEAN DEFAULT FALSE,
            created_by INT,
            updated_by INT,
            created_at DATETIME,
            updated_at DATETIME,
            FOREIGN KEY (created_by) REFERENCES users(id),
            FOREIGN KEY (updated_by) REFERENCES users(id)
        ) ENGINE=InnoDB
    ''')
    
    # Create variables table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS variables (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) UNIQUE,
            description TEXT,
            data_type VARCHAR(50),
            default_value TEXT,
            created_by INT,
            updated_by INT,
            created_at DATETIME,
            updated_at DATETIME,
            FOREIGN KEY (created_by) REFERENCES users(id),
            FOREIGN KEY (updated_by) REFERENCES users(id)
        ) ENGINE=InnoDB
    ''')
    
    # Create TTS settings table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS tts_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            engine VARCHAR(50),
            api_key TEXT,
            settings TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT,
            updated_by INT,
            created_at DATETIME,
            updated_at DATETIME,
            FOREIGN KEY (created_by) REFERENCES users(id),
            FOREIGN KEY (updated_by) REFERENCES users(id)
        ) ENGINE=InnoDB
    ''')
    
    # Create languages table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS languages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(10) UNIQUE,
            name VARCHAR(50),
            is_active BOOLEAN DEFAULT TRUE,
            created_at DATETIME
        ) ENGINE=InnoDB
    ''')
    
    # Create callers table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS callers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255),
            balance FLOAT,
            debt FLOAT,
            discount FLOAT,
            birthday VARCHAR(20),
            card_number VARCHAR(50),
            due_date VARCHAR(20),
            discount_due_date VARCHAR(20),
            created_at DATETIME
        ) ENGINE=InnoDB
    ''')
    
    # Create call logs table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS call_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            caller_id INT,
            caller_name VARCHAR(255),
            card_number VARCHAR(50),
            call_start_time DATETIME,
            call_end_time DATETIME,
            call_duration INT,
            verification_attempts INT DEFAULT 0,
            verification_successful BOOLEAN DEFAULT FALSE,
            information_repeated BOOLEAN DEFAULT FALSE,
            requested_agent BOOLEAN DEFAULT FALSE,
            agreed_to_payment_details BOOLEAN DEFAULT FALSE,
            payment_method_interest VARCHAR(255),
            follow_up_choice VARCHAR(255),
            call_status VARCHAR(50),
            notes TEXT,
            created_at DATETIME,
            FOREIGN KEY (caller_id) REFERENCES callers(id)
        ) ENGINE=InnoDB
    ''')
    
    # Insert default roles if they don't exist
    roles_data = [
        ('admin', 'Full access to all features'),
        ('supervisor', 'Access to reports and limited settings'),
        ('standard', 'Basic access to reports only')
    ]
    
    for role_name, role_desc in roles_data:
        cursor.execute('''
            INSERT INTO roles (name, description, created_at) 
            SELECT %s, %s, NOW() 
            FROM DUAL 
            WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = %s)
        ''', (role_name, role_desc, role_name))
    
    # Create default admin user if not exists
    cursor.execute('''
        INSERT INTO users (username, password, role_id, created_at) 
        SELECT 'admin', %s, (SELECT id FROM roles WHERE name = 'admin'), NOW()
        FROM DUAL 
        WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin')
    ''', (bcrypt.generate_password_hash('admin123').decode('utf-8'),))
    
    # Insert default language if not exists
    cursor.execute('''
        INSERT INTO languages (code, name, created_at) 
        SELECT 'en', 'English', NOW() 
        FROM DUAL 
        WHERE NOT EXISTS (SELECT 1 FROM languages WHERE code = 'en')
    ''')
    
    conn.commit()
    conn.close()

def get_all_callers():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    cursor.execute('SELECT * FROM callers')
    callers = cursor.fetchall()
    conn.close()
    return callers

def clear_all_callers():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    cursor.execute('DELETE FROM callers')
    conn.commit()
    conn.close()

def add_caller(name, balance, debt, discount, birthday, card_number, due_date, discount_due_date):
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO callers (name, balance, debt, discount, birthday, card_number, due_date, discount_due_date, created_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW())
    ''', (name, balance, debt, discount, birthday, card_number, due_date, discount_due_date))
    conn.commit()
    conn.close()

def add_callers_bulk(callers_data):
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    cursor.executemany('''
        INSERT INTO callers (name, balance, debt, discount, birthday, card_number, due_date, discount_due_date, created_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW())
    ''', callers_data)
    conn.commit()
    conn.close()

def log_call(user_id, caller_name, card_number, call_start_time, call_end_time,
             verification_attempts, verification_successful, information_repeated,
             requested_agent, agreed_to_payment_details, payment_method_interest,
             follow_up_choice, call_status, notes=""):
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    call_duration = 0
    if call_start_time and call_end_time:
        start = datetime.fromisoformat(call_start_time)
        end = datetime.fromisoformat(call_end_time)
        call_duration = int((end - start).total_seconds())
    cursor.execute('''
        INSERT INTO call_logs (user_id, caller_name, card_number, call_start_time, call_end_time,
                              call_duration, verification_attempts, verification_successful,
                              information_repeated, requested_agent, agreed_to_payment_details,
                              payment_method_interest, follow_up_choice, call_status, notes)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    ''', (user_id, caller_name, card_number, call_start_time, call_end_time, call_duration,
          verification_attempts, verification_successful, information_repeated, requested_agent,
          agreed_to_payment_details, payment_method_interest, follow_up_choice, call_status, notes))
    conn.commit()
    conn.close()

def get_all_call_logs():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    cursor.execute('''
        SELECT l.*, c.name as caller_name, c.balance, c.debt, c.discount
        FROM call_logs l
        LEFT JOIN callers c ON l.caller_id = c.id
        ORDER BY l.call_start_time DESC
    ''')
    logs = cursor.fetchall()
    cursor.close()
    conn.close()
    return logs

def get_call_statistics():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    cursor.execute('SELECT COUNT(*) as total_calls FROM call_logs')
    total_calls = cursor.fetchone()['total_calls']
    cursor.execute('SELECT COUNT(*) as successful FROM call_logs WHERE verification_successful = 1')
    successful_verifications = cursor.fetchone()['successful']
    cursor.execute('SELECT COUNT(*) as agent_requests FROM call_logs WHERE requested_agent = 1')
    agent_requests = cursor.fetchone()['agent_requests']
    cursor.execute('SELECT COUNT(*) as payment_agreed FROM call_logs WHERE agreed_to_payment_details = 1')
    payment_agreed = cursor.fetchone()['payment_agreed']
    cursor.execute('SELECT COUNT(*) as info_repeated FROM call_logs WHERE information_repeated = 1')
    info_repeated = cursor.fetchone()['info_repeated']
    cursor.execute('SELECT AVG(call_duration) as avg_duration FROM call_logs WHERE call_duration > 0')
    avg_duration = cursor.fetchone()['avg_duration'] or 0
    conn.close()
    return {
        'total_calls': total_calls,
        'successful_verifications': successful_verifications,
        'agent_requests': agent_requests,
        'payment_agreed': payment_agreed,
        'info_repeated': info_repeated,
        'avg_duration': round(avg_duration, 2),
        'verification_rate': round((successful_verifications / total_calls * 100) if total_calls > 0 else 0, 2),
        'agent_request_rate': round((agent_requests / total_calls * 100) if total_calls > 0 else 0, 2),
        'payment_agreement_rate': round((payment_agreed / total_calls * 100) if total_calls > 0 else 0, 2)
    }

@app.after_request
def add_no_cache_headers(response):
    response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    return response

@app.route('/')
@login_required
def index():
    """Main page showing current callers and upload options"""
    callers = get_all_callers()
    return render_template('index.html', callers=callers)

@app.route('/add_caller', methods=['GET', 'POST'])
@login_required
def add_caller_form():
    """Form to add a single caller"""
    if request.method == 'POST':
        try:
            name = request.form['name']
            balance = float(request.form['balance'])
            debt = float(request.form['debt'])
            discount = float(request.form['discount'])
            birthday = request.form['birthday']
            card_number = request.form['card_number']
            due_date = request.form['due_date']
            discount_due_date = request.form['discount_due_date']
            
            add_caller(name, balance, debt, discount, birthday, card_number, due_date, discount_due_date)
            flash('Caller added successfully!', 'success')
            return redirect(url_for('index'))
        except Exception as e:
            flash(f'Error adding caller: {str(e)}', 'error')
    
    return render_template('add_caller.html')

@app.route('/upload_csv', methods=['POST'])
@login_required
def upload_csv():
    """Upload callers from CSV file"""
    if 'file' not in request.files:
        flash('No file selected', 'error')
        return redirect(url_for('index'))
    
    file = request.files['file']
    if file.filename == '':
        flash('No file selected', 'error')
        return redirect(url_for('index'))
    
    if not file.filename.endswith('.csv'):
        flash('Please upload a CSV file', 'error')
        return redirect(url_for('index'))
    
    try:
        # Read CSV file
        stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
        csv_input = csv.reader(stream)
        
        # Skip header row if it exists
        header = next(csv_input, None)
        
        users_data = []
        for row in csv_input:
            if len(row) >= 8:  # Ensure we have all required fields
                users_data.append((
                    row[0],  # name
                    float(row[1]),  # balance
                    float(row[2]),  # debt
                    float(row[3]),  # discount
                    row[4],  # birthday
                    row[5],  # card_number
                    row[6],  # due_date
                    row[7]   # discount_due_date
                ))
        
        if users_data:
            add_callers_bulk(users_data)
            flash(f'Successfully uploaded {len(users_data)} callers!', 'success')
        else:
            flash('No valid caller data found in CSV file', 'error')
    
    except Exception as e:
        flash(f'Error processing CSV file: {str(e)}', 'error')
    
    return redirect(url_for('index'))

@app.route('/clear_callers', methods=['POST'])
@login_required
def clear_callers():
    """Clear all callers from the database"""
    try:
        clear_all_callers()
        flash('All callers cleared successfully!', 'success')
    except Exception as e:
        flash(f'Error clearing users: {str(e)}', 'error')
    
    return redirect(url_for('index'))

@app.route('/reports')
@login_required
@supervisor_required
def reports():
    """Reports dashboard page"""
    stats = get_call_statistics()
    recent_calls = get_all_call_logs()[:10]  # Get last 10 calls
    return render_template('reports.html', stats=stats, recent_calls=recent_calls)

@app.route('/call_logs')
@login_required
def call_logs():
    """Detailed call logs page"""
    logs = get_all_call_logs()
    return render_template('call_logs.html', logs=logs)

@app.route('/add_call_log', methods=['GET', 'POST'])
@login_required
def add_call_log():
    """Form to manually add a call log (for testing)"""
    if request.method == 'POST':
        try:
            caller_id = request.form.get('caller_id') or None
            caller_name = request.form['caller_name']
            card_number = request.form['card_number']
            call_start_time = request.form['call_start_time']
            call_end_time = request.form['call_end_time']
            verification_attempts = int(request.form['verification_attempts'])
            verification_successful = 'verification_successful' in request.form
            information_repeated = 'information_repeated' in request.form
            requested_agent = 'requested_agent' in request.form
            agreed_to_payment_details = 'agreed_to_payment_details' in request.form
            payment_method_interest = request.form.get('payment_method_interest', '')
            follow_up_choice = request.form['follow_up_choice']
            call_status = request.form['call_status']
            notes = request.form.get('notes', '')

            log_call(caller_id, caller_name, card_number, call_start_time, call_end_time,
                    verification_attempts, verification_successful, information_repeated,
                    requested_agent, agreed_to_payment_details, payment_method_interest,
                    follow_up_choice, call_status, notes)

            flash('Call log added successfully!', 'success')
            return redirect(url_for('call_logs'))
        except Exception as e:
            flash(f'Error adding call log: {str(e)}', 'error')

    callers = get_all_callers()
    return render_template('add_call_log.html', callers=callers)

@app.route('/delete_call_log/<int:log_id>', methods=['POST'])
@login_required
@supervisor_required
def delete_call_log(log_id):
    """Delete a call log by ID"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    cursor.execute('DELETE FROM call_logs WHERE id = %s', (log_id,))
    conn.commit()
    conn.close()
    flash('Call log deleted successfully.', 'success')
    return redirect(url_for('call_logs'))

@app.route('/delete_all_call_logs', methods=['POST'])
@login_required
@supervisor_required
def delete_all_call_logs():
    """Delete all call logs"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    cursor.execute('DELETE FROM call_logs')
    conn.commit()
    conn.close()
    flash('All call logs deleted successfully.', 'success')
    return redirect(url_for('call_logs'))

@app.route('/api/callers')
@login_required
def api_callers():
    """API endpoint to get all callers as JSON"""
    callers = get_all_callers()
    callers_list = []
    for caller in callers:
        callers_list.append({
            'id': caller['id'],
            'name': caller['name'],
            'balance': caller['balance'],
            'debt': caller['debt'],
            'discount': caller['discount'],
            'birthday': caller['birthday'],
            'card_number': caller['card_number'],
            'due_date': caller['due_date'],
            'discount_due_date': caller['discount_due_date']
        })
    return jsonify(callers_list)

@app.route('/api/call_logs')
@login_required
@supervisor_required
def api_call_logs():
    """API endpoint to get all call logs as JSON"""
    logs = get_all_call_logs()
    logs_list = []
    for log in logs:
        logs_list.append({
            'id': log['id'],
            'caller_id': log['caller_id'],
            'caller_name': log['caller_name'],
            'card_number': log['card_number'],
            'call_start_time': log['call_start_time'],
            'call_end_time': log['call_end_time'],
            'call_duration': log['call_duration'],
            'verification_attempts': log['verification_attempts'],
            'verification_successful': log['verification_successful'],
            'information_repeated': log['information_repeated'],
            'requested_agent': log['requested_agent'],
            'agreed_to_payment_details': log['agreed_to_payment_details'],
            'payment_method_interest': log['payment_method_interest'],
            'follow_up_choice': log['follow_up_choice'],
            'call_status': log['call_status'],
            'notes': log['notes'],
            'caller_name': log['caller_name'],
            'balance': log['balance'],
            'debt': log['debt'],
            'discount': log['discount']
        })
    return jsonify(logs_list)

@app.route('/api/statistics')
@login_required
@supervisor_required
def api_statistics():
    """API endpoint to get call statistics as JSON"""
    return jsonify(get_call_statistics())

@app.route('/ivr_editor')
@admin_required
def ivr_editor():
    """IVR Flow Editor page - Only accessible by admins"""
    return render_template('ivr_editor.html')

if __name__ == '__main__':
    init_database()
    app.run(debug=True, host='0.0.0.0', port=7000)
