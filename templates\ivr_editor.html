{% extends "base.html" %}

{% block title %}IVR Flow Editor - Servicedesk Automated Assistant{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2><i class="fas fa-project-diagram"></i> IVR Flow Editor</h2>
        <p class="text-muted">Coming soon! Design your IVR flow with drag-and-drop functionality.</p>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <!-- Component Palette -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">Components</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <div class="list-group-item">
                        <i class="fas fa-comment"></i> Prompt Node
                        <small class="text-muted d-block">Play a prompt</small>
                    </div>
                    <div class="list-group-item">
                        <i class="fas fa-microphone"></i> Input Node
                        <small class="text-muted d-block">Get user input</small>
                    </div>
                    <div class="list-group-item">
                        <i class="fas fa-code-branch"></i> Decision Node
                        <small class="text-muted d-block">Branch based on condition</small>
                    </div>
                    <div class="list-group-item">
                        <i class="fas fa-phone-volume"></i> Transfer Node
                        <small class="text-muted d-block">Transfer to agent</small>
                    </div>
                    <div class="list-group-item">
                        <i class="fas fa-database"></i> API Node
                        <small class="text-muted d-block">Call external API</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Properties Panel -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0">Properties</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Select a node to view and edit its properties</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-9">
        <!-- Canvas -->
        <div class="card shadow">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">Flow Canvas</h5>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary" disabled>
                            <i class="fas fa-save"></i> Save
                        </button>
                        <button class="btn btn-outline-success" disabled>
                            <i class="fas fa-play"></i> Test
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body bg-light" style="min-height: 600px">
                <div class="text-center py-5">
                    <i class="fas fa-project-diagram fa-4x text-muted mb-3"></i>
                    <h4>Visual IVR Flow Editor</h4>
                    <p class="text-muted">
                        Coming soon! You'll be able to design your call flow by dragging and dropping components.<br>
                        Connect nodes, set conditions, and create complex IVR flows visually.
                    </p>
                    <hr class="my-4">
                    <h5>Planned Features:</h5>
                    <ul class="list-unstyled">
                        <li>✨ Drag & Drop Interface</li>
                        <li>🔗 Visual Connection Builder</li>
                        <li>🎯 Conditional Branching</li>
                        <li>🔄 Flow Testing & Validation</li>
                        <li>📊 Flow Analytics</li>
                        <li>📱 Mobile-Friendly Design</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Minimap -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0">Flow Overview</h5>
            </div>
            <div class="card-body bg-light" style="height: 200px">
                <div class="text-center py-4">
                    <p class="text-muted mb-0">
                        The minimap will show a bird's-eye view of your IVR flow
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    // Future: Initialize flow editor
    // - Set up drag & drop
    // - Initialize connection lines
    // - Set up property panel updates
    // - Initialize flow validation
    // - Set up flow testing
});
</script>
{% endblock %}
