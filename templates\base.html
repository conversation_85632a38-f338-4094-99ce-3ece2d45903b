<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Servicedesk Automated Assistant{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Add Select2 for better dropdowns -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .navbar-nav .nav-item.dropdown:hover .dropdown-menu {
            display: block;
        }
        .select2-container {
            width: 100% !important;
        }
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        main {
            flex: 1;
        }
        .login-page .navbar {
            display: none;
        }
    </style>
</head>
<body {% if request.endpoint == 'login' %}class="login-page"{% endif %}>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-headset"></i> Servicedesk Assistant
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                        <!-- Prompt Management Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="promptsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-comment-dots"></i> Prompts
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('prompts_list') }}">View All Prompts</a></li>
                                {% if current_user.role_name in ['admin'] %}
                                    <li><a class="dropdown-item" href="{{ url_for('create_prompt') }}">Create New Prompt</a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('manage_variables') }}">Manage Variables</a></li>
                                {% endif %}
                            </ul>
                        </li>

                        <!-- Caller Management Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="callersDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-users"></i> Callers
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('index') }}">View All Callers</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('upload_csv') }}">Upload Callers</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('add_caller_form') }}">Add Single Caller</a></li>
                            </ul>
                        </li>

                        <!-- Reports Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-chart-bar"></i> Reports
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('call_logs') }}">Call Logs</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('reports') }}">Analytics</a></li>
                                {% if current_user.role_name in ['admin', 'supervisor'] %}
                                    <li><a class="dropdown-item" href="{{ url_for('audit_logs') }}">Audit Logs</a></li>
                                {% endif %}
                            </ul>
                        </li>

                        {% if current_user.role_name in ['admin'] %}
                            <!-- Settings Dropdown -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="settingsDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cog"></i> Settings
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{{ url_for('user_management') }}">User Management</a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('tts_settings') }}">TTS Configuration</a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('language_settings') }}">Language Settings</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ url_for('ivr_editor') }}">IVR Flow Editor</a></li>
                                </ul>
                            </li>
                        {% endif %}
                    {% endif %}
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav ms-auto">
                    {% if current_user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ current_user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{{ url_for('profile') }}">Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
