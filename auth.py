from functools import wraps
from flask import jsonify, request, flash, redirect, url_for
from flask_login import login_required, current_user, login_user, logout_user
from flask_jwt_extended import jwt_required, get_jwt_identity
import mysql.connector
from datetime import datetime, timedelta
from config import MYSQL_CONFIG, bcrypt
from models import User

def get_db_connection():
    return mysql.connector.connect(**MYSQL_CONFIG)

def log_audit(user_id, action, details, ip_address=None):
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute('''
            INSERT INTO audit_logs (user_id, action, details, ip_address, created_at)
            VALUES (%s, %s, %s, %s, NOW())
        ''', (user_id, action, details, ip_address))
        conn.commit()
    finally:
        cursor.close()
        conn.close()

def admin_required(fn):
    @wraps(fn)
    @login_required
    def wrapper(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('login'))
            
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute('''
                SELECT r.name as role_name 
                FROM users u 
                JOIN roles r ON u.role_id = r.id 
                WHERE u.id = %s
            ''', (current_user.id,))
            result = cursor.fetchone()
            if not result or result['role_name'] != 'admin':
                flash('Admin privileges required to access this page.', 'error')
                return redirect(url_for('index'))
            return fn(*args, **kwargs)
        finally:
            cursor.close()
            conn.close()
    return wrapper

def supervisor_required(fn):
    @wraps(fn)
    @login_required
    def wrapper(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('login'))
            
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute('''
                SELECT r.name as role_name 
                FROM users u 
                JOIN roles r ON u.role_id = r.id 
                WHERE u.id = %s
            ''', (current_user.id,))
            result = cursor.fetchone()
            if not result or result['role_name'] not in ['admin', 'supervisor']:
                flash('Supervisor privileges required to access this page.', 'error')
                return redirect(url_for('index'))
            return fn(*args, **kwargs)
        finally:
            cursor.close()
            conn.close()
    return wrapper

def get_user_by_username(username):
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute('''
            SELECT u.*, r.name as role_name 
            FROM system_users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.username = %s
        ''', (username,))
        return cursor.fetchone()
    finally:
        cursor.close()
        conn.close()

def create_user(username, password, email, role_id):
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
        cursor.execute('''
            INSERT INTO system_users (username, password_hash, email, role_id)
            VALUES (%s, %s, %s, %s)
        ''', (username, password_hash, email, role_id))
        conn.commit()
        return cursor.lastrowid
    finally:
        cursor.close()
        conn.close()

def update_user(user_id, email=None, role_id=None, is_active=None):
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        updates = []
        values = []
        if email is not None:
            updates.append("email = %s")
            values.append(email)
        if role_id is not None:
            updates.append("role_id = %s")
            values.append(role_id)
        if is_active is not None:
            updates.append("is_active = %s")
            values.append(is_active)
        
        if updates:
            values.append(user_id)
            query = f"UPDATE system_users SET {', '.join(updates)} WHERE id = %s"
            cursor.execute(query, values)
            conn.commit()
            return True
        return False
    finally:
        cursor.close()
        conn.close()

def update_last_login(user_id):
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute('''
            UPDATE system_users 
            SET last_login = CURRENT_TIMESTAMP 
            WHERE id = %s
        ''', (user_id,))
        conn.commit()
    finally:
        cursor.close()
        conn.close()
