{% extends "base.html" %}

{% block title %}Reports Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h1><i class="fas fa-chart-bar"></i> Reports Dashboard</h1>
        <p class="lead">Call center performance and caller interaction analytics</p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.total_calls }}</h4>
                        <p class="mb-0">Total Calls</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-phone fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.verification_rate }}%</h4>
                        <p class="mb-0">Verification Rate</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.payment_agreement_rate }}%</h4>
                        <p class="mb-0">Payment Agreement</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-credit-card fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.agent_request_rate }}%</h4>
                        <p class="mb-0">Agent Requests</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-tie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> Call Statistics</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Total Calls:</strong></td>
                        <td>{{ stats.total_calls }}</td>
                    </tr>
                    <tr>
                        <td><strong>Successful Verifications:</strong></td>
                        <td>{{ stats.successful_verifications }}</td>
                    </tr>
                    <tr>
                        <td><strong>Agent Requests:</strong></td>
                        <td>{{ stats.agent_requests }}</td>
                    </tr>
                    <tr>
                        <td><strong>Payment Agreements:</strong></td>
                        <td>{{ stats.payment_agreed }}</td>
                    </tr>
                    <tr>
                        <td><strong>Information Repeated:</strong></td>
                        <td>{{ stats.info_repeated }}</td>
                    </tr>
                    <tr>
                        <td><strong>Average Call Duration:</strong></td>
                        <td>{{ stats.avg_duration }} seconds</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-percentage"></i> Performance Rates</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Verification Success Rate</label>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: {{ stats.verification_rate }}%">
                            {{ stats.verification_rate }}%
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Payment Agreement Rate</label>
                    <div class="progress">
                        <div class="progress-bar bg-info" style="width: {{ stats.payment_agreement_rate }}%">
                            {{ stats.payment_agreement_rate }}%
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Agent Request Rate</label>
                    <div class="progress">
                        <div class="progress-bar bg-warning" style="width: {{ stats.agent_request_rate }}%">
                            {{ stats.agent_request_rate }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Calls -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-clock"></i> Recent Calls</h5>
                <a href="{{ url_for('call_logs') }}" class="btn btn-sm btn-primary">View All Logs</a>
            </div>
            <div class="card-body">
                {% if recent_calls %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Time</th>
                                <th>Caller</th>
                                <th>Card Number</th>
                                <th>Duration</th>
                                <th>Verified</th>
                                <th>Payment Agreed</th>
                                <th>Agent Requested</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for call in recent_calls %}
                            <tr>
                                <td>{{ call.call_start_time[:16] if call.call_start_time else 'N/A' }}</td>
                                <td>{{ call.caller_name }}</td>
                                <td>{{ call.card_number }}</td>
                                <td>{{ call.call_duration }}s</td>
                                <td>
                                    {% if call.verification_successful %}
                                        <span class="badge bg-success">Yes</span>
                                    {% else %}
                                        <span class="badge bg-danger">No</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if call.agreed_to_payment_details %}
                                        <span class="badge bg-success">Yes</span>
                                    {% else %}
                                        <span class="badge bg-secondary">No</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if call.requested_agent %}
                                        <span class="badge bg-warning">Yes</span>
                                    {% else %}
                                        <span class="badge bg-secondary">No</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ call.call_status }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-phone fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No call logs found</h5>
                    <p class="text-muted">Call logs will appear here once the agent system starts logging interactions.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
