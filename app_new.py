 # Import at the top of app.py
import os
from dotenv import load_dotenv
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_jwt_extended import JW<PERSON>anager, jwt_required, create_access_token, get_jwt_identity
from flask_bcrypt import Bcrypt
from flask_cors import CORS
import mysql.connector
import csv
import io
from datetime import datetime, timedelta
from auth import (
    init_jwt, bcrypt, admin_required, supervisor_required,
    get_user_by_username, create_user, update_user, update_last_login,
    log_audit
)

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-change-this')
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-key-change-this')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)

# MySQL Configuration
MYSQL_CONFIG = {
    'host': os.getenv('MYSQL_HOST', 'localhost'),
    'user': os.getenv('MYSQL_USER', 'root'),
    'password': os.getenv('MYSQL_PASSWORD', ''),
    'database': os.getenv('MYSQL_DATABASE', 'assistant')
}

# Initialize extensions
jwt = init_jwt(app)
bcrypt = Bcrypt(app)
CORS(app)

# Import routes after app initialization to avoid circular imports
from routes.auth_routes import *
from routes.prompt_routes import *
from routes.tts_routes import *

def init_database():
    # (existing init_database function remains the same)
