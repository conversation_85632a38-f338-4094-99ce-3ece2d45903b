# Authentication and User Management Routes
@app.route('/api/auth/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({"msg": "Missing username or password"}), 400
    
    user = get_user_by_username(username)
    if not user or not bcrypt.check_password_hash(user['password_hash'], password):
        return jsonify({"msg": "Invalid username or password"}), 401
    
    if not user['is_active']:
        return jsonify({"msg": "Account is disabled"}), 403
    
    # Create access token and update last login
    access_token = create_access_token(identity=user['id'])
    update_last_login(user['id'])
    
    # Log the successful login
    log_audit(
        user['id'],
        'login',
        f"User {username} logged in",
        request.remote_addr
    )
    
    return jsonify({
        "token": access_token,
        "user": {
            "id": user['id'],
            "username": user['username'],
            "email": user['email'],
            "role": user['role_name']
        }
    })

@app.route('/api/users', methods=['GET'])
@admin_required
def list_users():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute('''
            SELECT u.id, u.username, u.email, u.is_active, 
                   u.last_login, r.name as role_name
            FROM system_users u
            JOIN roles r ON u.role_id = r.id
            ORDER BY u.username
        ''')
        users = cursor.fetchall()
        return jsonify({"users": users})
    finally:
        cursor.close()
        conn.close()

@app.route('/api/users', methods=['POST'])
@admin_required
def create_new_user():
    data = request.get_json()
    required_fields = ['username', 'password', 'email', 'role_id']
    
    if not all(field in data for field in required_fields):
        return jsonify({"msg": "Missing required fields"}), 400
    
    # Check if username already exists
    if get_user_by_username(data['username']):
        return jsonify({"msg": "Username already exists"}), 409
    
    try:
        user_id = create_user(
            data['username'],
            data['password'],
            data['email'],
            data['role_id']
        )
        
        # Log the user creation
        current_user_id = get_jwt_identity()
        log_audit(
            current_user_id,
            'create_user',
            f"Created user {data['username']}",
            request.remote_addr
        )
        
        return jsonify({
            "msg": "User created successfully",
            "user_id": user_id
        }), 201
    except Exception as e:
        return jsonify({"msg": f"Error creating user: {str(e)}"}), 500

@app.route('/api/users/<int:user_id>', methods=['PUT'])
@admin_required
def update_existing_user(user_id):
    data = request.get_json()
    try:
        updated = update_user(
            user_id,
            email=data.get('email'),
            role_id=data.get('role_id'),
            is_active=data.get('is_active')
        )
        
        if updated:
            # Log the user update
            current_user_id = get_jwt_identity()
            log_audit(
                current_user_id,
                'update_user',
                f"Updated user {user_id}",
                request.remote_addr
            )
            return jsonify({"msg": "User updated successfully"})
        return jsonify({"msg": "No changes made"}), 304
    except Exception as e:
        return jsonify({"msg": f"Error updating user: {str(e)}"}), 500

@app.route('/api/roles', methods=['GET'])
@jwt_required()
def list_roles():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute('SELECT * FROM roles ORDER BY name')
        roles = cursor.fetchall()
        return jsonify({"roles": roles})
    finally:
        cursor.close()
        conn.close()

@app.route('/api/audit-logs', methods=['GET'])
@supervisor_required
def list_audit_logs():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    try:
        # Support filtering by date range and user
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        user_id = request.args.get('user_id')
        
        query = '''
            SELECT al.*, u.username 
            FROM audit_logs al
            JOIN system_users u ON al.user_id = u.id
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            query += ' AND al.created_at >= %s'
            params.append(start_date)
        if end_date:
            query += ' AND al.created_at <= %s'
            params.append(end_date)
        if user_id:
            query += ' AND al.user_id = %s'
            params.append(user_id)
            
        query += ' ORDER BY al.created_at DESC LIMIT 1000'
        
        cursor.execute(query, params)
        logs = cursor.fetchall()
        return jsonify({"audit_logs": logs})
    finally:
        cursor.close()
        conn.close()
